<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>资讯详情 - 金融智能体</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: "#3B82F6",
            secondary: "#F1F5F9",
          },
        },
      },
    };
  </script>
</head>
<body class="bg-gray-50 min-h-screen">
  <!-- 顶部导航栏 -->
  <nav class="fixed top-0 w-full bg-white z-50 px-4 py-3 border-b border-gray-100">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <button onclick="goBack()" class="w-8 h-8 flex items-center justify-center mr-3 cursor-pointer">
          <i class="ri-arrow-left-line text-gray-600 text-lg"></i>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">资讯详情</h1>
      </div>
      <div class="flex items-center space-x-2">
        <!-- 智能体分析按钮 -->
        <button onclick="openAIChat()" class="w-8 h-8 flex items-center justify-center">
          <i class="ri-robot-line text-gray-600 text-lg"></i>
        </button>
        <!-- 微信小程序分享按钮 -->
        <button onclick="shareNews()" class="w-8 h-8 flex items-center justify-center">
          <div class="flex space-x-0.5">
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
          </div>
        </button>
        <!-- 微信小程序返回主界面按钮 -->
        <button class="w-8 h-8 flex items-center justify-center">
          <div class="w-5 h-5 border-2 border-gray-600 rounded-full flex items-center justify-center">
            <div class="w-2 h-2 bg-gray-600 rounded-full"></div>
          </div>
        </button>
      </div>
    </div>
  </nav>

  <!-- 主要内容区域 -->
  <main class="pt-16 pb-20 px-4">
    <!-- 新闻标题和信息 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="flex items-center space-x-2 mb-3">
          <span id="news-category" class="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">重要</span>
          <span id="news-source" class="text-xs text-gray-500">央行政策</span>
        </div>
        
        <h1 id="news-title" class="text-xl font-bold text-gray-900 mb-4 leading-relaxed">
          央行宣布降准0.5个百分点，释放长期资金约1万亿元
        </h1>
        
        <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
          <div class="flex items-center space-x-4">
            <span id="news-time">2024-07-24 14:30</span>
            <span id="news-author">财经新闻</span>
          </div>
          <div class="flex items-center space-x-1">
            <i class="ri-eye-line text-gray-400"></i>
            <span id="news-views">1.2万</span>
          </div>
        </div>
        
        <!-- 新闻图片 -->
        <div class="mb-4">
          <img id="news-image" src="https://via.placeholder.com/400x200/3B82F6/FFFFFF?text=央行降准" alt="新闻图片" class="w-full h-48 object-cover rounded-lg">
        </div>
      </div>
    </section>

    <!-- 新闻正文 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div id="news-content" class="prose prose-sm max-w-none text-gray-700 leading-relaxed">
          <p class="mb-4">
            中国人民银行今日宣布，为支持实体经济发展，促进综合融资成本稳中有降，决定于2024年7月25日下调金融机构存款准备金率0.5个百分点（不含已执行5%存款准备金率的金融机构）。
          </p>
          
          <p class="mb-4">
            本次降准共释放长期资金约1万亿元。央行有关负责人表示，此次降准是货币政策工具的常规性操作，体现了稳健货币政策的灵活适度，有利于保持银行体系流动性合理充裕，促进货币信贷合理增长。
          </p>
          
          <p class="mb-4">
            <strong>市场影响分析：</strong>
          </p>
          
          <ul class="mb-4 space-y-2">
            <li>• 银行股有望受益，流动性改善将降低银行资金成本</li>
            <li>• 房地产行业可能迎来利好，有助于缓解行业流动性压力</li>
            <li>• 债券市场短期可能面临调整压力</li>
            <li>• 人民币汇率可能承受一定下行压力</li>
          </ul>
          
          <p class="mb-4">
            业内专家认为，此次降准释放的资金将主要用于支持小微企业、民营企业等实体经济发展，有助于进一步降低社会融资成本，提振市场信心。
          </p>
          
          <p class="mb-4">
            <strong>后续政策展望：</strong>
          </p>
          
          <p class="mb-4">
            分析师普遍认为，在当前经济环境下，央行仍有进一步宽松的空间。预计后续可能还会有定向降准、降息等政策工具的运用，以确保流动性合理充裕，支持经济稳定增长。
          </p>
        </div>
      </div>
    </section>

    <!-- AI分析 -->
    <section class="mb-6">
      <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 border border-blue-100">
        <div class="flex items-center mb-3">
          <i class="ri-robot-line text-primary text-lg mr-2"></i>
          <span class="font-semibold text-gray-900">AI智能分析</span>
        </div>
        
        <div class="space-y-3">
          <div class="bg-white rounded-lg p-3">
            <h4 class="font-medium text-gray-900 mb-2">投资机会</h4>
            <p class="text-sm text-gray-700">
              银行股、地产股可能受益于流动性改善，建议关注优质银行股的投资机会。
            </p>
          </div>
          
          <div class="bg-white rounded-lg p-3">
            <h4 class="font-medium text-gray-900 mb-2">风险提示</h4>
            <p class="text-sm text-gray-700">
              需关注通胀预期和汇率波动风险，建议分散投资，控制仓位。
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- 相关股票 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <h3 class="font-semibold text-gray-900 mb-4">相关受益股票</h3>
        <div class="space-y-3">
          <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span class="text-blue-600 font-bold text-xs">招</span>
              </div>
              <div>
                <span class="font-medium text-gray-900">招商银行</span>
                <span class="text-sm text-gray-500 ml-2">600036</span>
              </div>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900">¥32.45</div>
              <div class="text-xs text-green-600">+2.1%</div>
            </div>
          </div>
          
          <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <span class="text-green-600 font-bold text-xs">平</span>
              </div>
              <div>
                <span class="font-medium text-gray-900">平安银行</span>
                <span class="text-sm text-gray-500 ml-2">000001</span>
              </div>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900">¥12.68</div>
              <div class="text-xs text-green-600">+1.8%</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 操作按钮 -->
    <section class="mb-6">
      <div class="grid grid-cols-2 gap-4">
        <button onclick="collectNews()" class="flex items-center justify-center py-3 bg-white border border-gray-200 rounded-lg text-gray-700">
          <i class="ri-heart-line mr-2"></i>
          收藏
        </button>
        <button onclick="shareNews()" class="flex items-center justify-center py-3 bg-primary text-white rounded-lg">
          <i class="ri-share-line mr-2"></i>
          分享
        </button>
      </div>
    </section>
  </main>

  <script>
    // 新闻数据
    const newsData = {
      'news1': {
        title: '央行宣布降准0.5个百分点，释放长期资金约1万亿元',
        category: '重要',
        source: '央行政策',
        time: '2024-07-24 14:30',
        author: '财经新闻',
        views: '1.2万',
        image: 'https://via.placeholder.com/400x200/3B82F6/FFFFFF?text=央行降准'
      },
      'news2': {
        title: 'A股三大指数集体上涨，创业板指涨超2%，银行股领涨',
        category: '市场',
        source: 'A股动态',
        time: '2024-07-24 11:30',
        author: '市场快讯',
        views: '8.5千',
        image: 'https://via.placeholder.com/400x200/10B981/FFFFFF?text=股市上涨'
      }
    };

    function goBack() {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        window.location.href = 'news.html';
      }
    }

    function openAIChat() {
      window.location.href = 'AI选股.html#ai-chat';
    }

    function shareNews() {
      const title = document.getElementById('news-title').textContent;
      if (navigator.share) {
        navigator.share({
          title: title,
          text: '来自金融智能体的最新资讯',
          url: window.location.href
        }).catch(err => {
          console.log('分享失败:', err);
        });
      } else {
        if (navigator.clipboard) {
          navigator.clipboard.writeText(window.location.href).then(() => {
            alert('链接已复制到剪贴板');
          });
        }
      }
    }

    function collectNews() {
      alert('收藏成功！');
    }

    // 页面加载时根据URL参数加载对应新闻
    document.addEventListener('DOMContentLoaded', function() {
      const urlParams = new URLSearchParams(window.location.search);
      const newsId = urlParams.get('id') || 'news1';
      
      if (newsData[newsId]) {
        const news = newsData[newsId];
        document.getElementById('news-title').textContent = news.title;
        document.getElementById('news-category').textContent = news.category;
        document.getElementById('news-source').textContent = news.source;
        document.getElementById('news-time').textContent = news.time;
        document.getElementById('news-author').textContent = news.author;
        document.getElementById('news-views').textContent = news.views;
        document.getElementById('news-image').src = news.image;
      }
    });
  </script>
</body>
</html>
