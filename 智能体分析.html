<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体分析 - 金融智能体</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
      :root {
        --primary-color: #3b82f6;
        --primary-dark: #2563eb;
        --primary-light: #dbeafe;
      }
      
      .primary { color: var(--primary-color); }
      .bg-primary { background-color: var(--primary-color); }
      .border-primary { border-color: var(--primary-color); }
      .text-primary { color: var(--primary-color); }
      
      .ai-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }
      
      .chat-bubble {
        animation: fadeInUp 0.3s ease-out;
      }
      
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      .typing-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #9ca3af;
        animation: typing 1.4s infinite ease-in-out;
      }
      
      .typing-indicator:nth-child(1) { animation-delay: -0.32s; }
      .typing-indicator:nth-child(2) { animation-delay: -0.16s; }
      
      @keyframes typing {
        0%, 80%, 100% {
          transform: scale(0);
          opacity: 0.5;
        }
        40% {
          transform: scale(1);
          opacity: 1;
        }
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm sticky top-0 z-40">
      <div class="flex items-center justify-between px-4 py-3">
        <div class="flex items-center space-x-3">
          <button onclick="goBack()" class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <i class="ri-arrow-left-line text-xl text-gray-600"></i>
          </button>
          <h1 class="text-lg font-semibold text-gray-900">智能体分析</h1>
        </div>
        <div class="flex items-center space-x-2">
          <button onclick="shareApp()" class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <i class="ri-share-line text-xl text-gray-600"></i>
          </button>
        </div>
      </div>
    </header>

    <main class="px-4 py-6 pb-20">
      <!-- 智能体介绍 -->
      <section class="mb-6">
        <div class="ai-card rounded-xl p-6 text-center">
          <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="ri-robot-line text-white text-2xl"></i>
          </div>
          <h2 class="text-xl font-bold mb-2">AI金融智能体</h2>
          <p class="text-white text-opacity-90 text-sm">专业的金融分析助手，为您提供个性化投资建议</p>
        </div>
      </section>

      <!-- 对话历史 -->
      <section class="mb-4">
        <div class="bg-white rounded-xl p-4 shadow-sm max-h-96 overflow-y-auto" id="chat-history">
          <!-- 欢迎消息 -->
          <div class="chat-bubble mb-4">
            <div class="flex items-start space-x-3">
              <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-teal-600 rounded-full flex items-center justify-center flex-shrink-0">
                <i class="ri-robot-line text-white text-sm"></i>
              </div>
              <div class="bg-gray-100 rounded-lg p-3 max-w-xs">
                <p class="text-sm text-gray-800">您好！我是您的专属金融智能体，可以为您提供个性化的投资建议。请选择您感兴趣的分析类型：</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 快速选项 -->
      <section class="mb-4">
        <div class="bg-white rounded-xl p-4 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-3">快速分析选项</h3>
          <div class="grid grid-cols-2 gap-2">
            <button class="analysis-option p-3 border border-gray-200 rounded-lg text-left hover:border-primary hover:bg-blue-50 transition-all">
              <i class="ri-line-chart-line text-primary mr-2"></i>
              <div class="text-sm font-medium">技术分析</div>
              <div class="text-xs text-gray-500">K线图表分析</div>
            </button>
            <button class="analysis-option p-3 border border-gray-200 rounded-lg text-left hover:border-primary hover:bg-blue-50 transition-all">
              <i class="ri-file-chart-line text-primary mr-2"></i>
              <div class="text-sm font-medium">基本面分析</div>
              <div class="text-xs text-gray-500">财务数据分析</div>
            </button>
            <button class="analysis-option p-3 border border-gray-200 rounded-lg text-left hover:border-primary hover:bg-blue-50 transition-all">
              <i class="ri-global-line text-primary mr-2"></i>
              <div class="text-sm font-medium">行业分析</div>
              <div class="text-xs text-gray-500">板块趋势分析</div>
            </button>
            <button class="analysis-option p-3 border border-gray-200 rounded-lg text-left hover:border-primary hover:bg-blue-50 transition-all">
              <i class="ri-shield-check-line text-primary mr-2"></i>
              <div class="text-sm font-medium">风险评估</div>
              <div class="text-xs text-gray-500">投资风险分析</div>
            </button>
          </div>
        </div>
      </section>

      <!-- 输入区域 -->
      <section>
        <div class="bg-white rounded-xl p-4 shadow-sm">
          <div class="flex space-x-3">
            <input
              type="text"
              id="user-input"
              placeholder="输入股票代码或问题，如：分析一下贵州茅台"
              class="flex-1 px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-primary"
            >
            <button id="send-message" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-600 transition-colors">
              <i class="ri-send-plane-line"></i>
            </button>
          </div>
        </div>
      </section>
    </main>

    <!-- 底部导航栏 -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
      <div class="grid grid-cols-4 h-16">
        <button onclick="window.location.href='index.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-apps-2-line text-lg text-gray-500"></i>
          </div>
          <span class="text-xs text-gray-500">首页</span>
        </button>
        <button onclick="window.location.href='自选股.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-star-line text-lg text-gray-500"></i>
          </div>
          <span class="text-xs text-gray-500">自选股</span>
        </button>
        <button onclick="window.location.href='reports.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-file-list-line text-lg text-gray-500"></i>
          </div>
          <span class="text-xs text-gray-500">报告中心</span>
        </button>
        <button onclick="window.location.href='我的.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-user-line text-lg text-gray-500"></i>
          </div>
          <span class="text-xs text-gray-500">我的</span>
        </button>
      </div>
    </nav>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const chatHistory = document.getElementById('chat-history');
        const userInput = document.getElementById('user-input');
        const sendButton = document.getElementById('send-message');

        // 分析选项点击
        document.querySelectorAll('.analysis-option').forEach(option => {
          option.addEventListener('click', function() {
            const analysisType = this.querySelector('.text-sm').textContent;
            addUserMessage(`我想要${analysisType}`);
            setTimeout(() => addAIResponse(analysisType), 1000);
          });
        });

        // 发送消息
        function sendMessage() {
          const message = userInput.value.trim();
          if (message) {
            addUserMessage(message);
            userInput.value = '';
            setTimeout(() => addAIResponse(message), 1000);
          }
        }

        sendButton.addEventListener('click', sendMessage);
        userInput.addEventListener('keypress', function(e) {
          if (e.key === 'Enter') sendMessage();
        });

        // 添加用户消息
        function addUserMessage(message) {
          const messageDiv = document.createElement('div');
          messageDiv.className = 'chat-bubble mb-4 flex justify-end';
          messageDiv.innerHTML = `
            <div class="bg-primary text-white rounded-lg p-3 max-w-xs">
              <p class="text-sm">${message}</p>
            </div>
          `;
          chatHistory.appendChild(messageDiv);
          chatHistory.scrollTop = chatHistory.scrollHeight;
        }

        // 添加AI回复
        function addAIResponse(userMessage) {
          // 显示打字指示器
          const typingDiv = document.createElement('div');
          typingDiv.className = 'chat-bubble mb-4';
          typingDiv.innerHTML = `
            <div class="flex items-start space-x-3">
              <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-teal-600 rounded-full flex items-center justify-center flex-shrink-0">
                <i class="ri-robot-line text-white text-sm"></i>
              </div>
              <div class="bg-gray-100 rounded-lg p-3">
                <div class="flex space-x-1">
                  <div class="typing-indicator"></div>
                  <div class="typing-indicator"></div>
                  <div class="typing-indicator"></div>
                </div>
              </div>
            </div>
          `;
          chatHistory.appendChild(typingDiv);
          chatHistory.scrollTop = chatHistory.scrollHeight;

          // 2秒后替换为实际回复
          setTimeout(() => {
            typingDiv.remove();
            const responseDiv = document.createElement('div');
            responseDiv.className = 'chat-bubble mb-4';
            responseDiv.innerHTML = `
              <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-teal-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <i class="ri-robot-line text-white text-sm"></i>
                </div>
                <div class="bg-gray-100 rounded-lg p-3 max-w-xs">
                  <p class="text-sm text-gray-800">${generateAIResponse(userMessage)}</p>
                </div>
              </div>
            `;
            chatHistory.appendChild(responseDiv);
            chatHistory.scrollTop = chatHistory.scrollHeight;
          }, 2000);
        }

        // 生成AI回复
        function generateAIResponse(userMessage) {
          const responses = {
            '技术分析': '基于技术指标分析，建议关注MACD金叉、RSI超卖反弹等信号。当前市场处于震荡整理阶段，建议等待明确突破信号。',
            '基本面分析': '从财务数据看，建议关注ROE>15%、PE<20倍的优质标的。重点关注现金流稳定、负债率合理的公司。',
            '行业分析': '当前新能源、人工智能、生物医药等板块具有较好的成长性。建议配置行业龙头，规避政策风险。',
            '风险评估': '当前市场风险偏好较低，建议控制仓位在60%以下，分散投资，设置止损位。关注宏观政策变化。'
          };

          // 检查是否包含股票代码或名称
          if (userMessage.includes('茅台') || userMessage.includes('600519')) {
            return '贵州茅台(600519)：白酒龙头，品牌价值突出。当前估值合理，建议长期持有。技术面看，股价在重要支撑位附近，可考虑分批建仓。';
          }

          // 根据关键词返回对应回复
          for (const [key, value] of Object.entries(responses)) {
            if (userMessage.includes(key)) {
              return value;
            }
          }

          return '感谢您的提问！我正在分析相关数据，请稍等片刻。如需更详细的分析，建议您提供具体的股票代码或分析需求。';
        }

        // 返回功能
        window.goBack = function() {
          if (window.history.length > 1) {
            window.history.back();
          } else {
            window.location.href = 'index.html';
          }
        };

        // 分享功能
        window.shareApp = function() {
          if (navigator.share) {
            navigator.share({
              title: '金融智能体 - 智能体分析',
              text: 'AI智能分析，专业投资建议',
              url: window.location.href
            }).catch(err => {
              console.log('分享失败:', err);
            });
          } else {
            if (navigator.clipboard) {
              navigator.clipboard.writeText(window.location.href).then(() => {
                alert('链接已复制到剪贴板');
              });
            }
          }
        };
      });
    </script>
  </body>
</html>
