<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>股票搜索分析 - 金融智能体</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
  <style>
    .primary { color: #3b82f6; }
    .bg-primary { background-color: #3b82f6; }
    .text-primary { color: #3b82f6; }
    .border-primary { border-color: #3b82f6; }
    .analyzing {
      animation: pulse 2s infinite;
    }
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }
    .loading-dots::after {
      content: '';
      animation: dots 1.5s infinite;
    }
    @keyframes dots {
      0%, 20% { content: ''; }
      40% { content: '.'; }
      60% { content: '..'; }
      80%, 100% { content: '...'; }
    }
  </style>
</head>
<body class="bg-gray-50">
  <!-- 顶部导航栏 -->
  <header class="bg-white shadow-sm fixed top-0 left-0 right-0 z-50">
    <div class="flex items-center justify-between px-4 py-3">
      <div class="flex items-center space-x-3">
        <button onclick="goBack()" class="p-2 hover:bg-gray-100 rounded-lg">
          <i class="ri-arrow-left-line text-xl text-gray-600"></i>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">股票分析</h1>
      </div>
      <div class="flex items-center space-x-2">
        <button onclick="addToWatchlist()" class="p-2 hover:bg-gray-100 rounded-lg">
          <i class="ri-star-line text-xl text-gray-600"></i>
        </button>
      </div>
    </div>
  </header>

  <!-- 主要内容区域 -->
  <main class="pt-16 pb-20 px-4">
    <!-- 搜索输入 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="flex items-center space-x-3">
          <div class="flex-1 relative">
            <input 
              type="text" 
              id="stockInput" 
              placeholder="输入股票代码或名称，如：600519 或 贵州茅台"
              class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
              onkeypress="handleEnterKey(event)"
            >
            <i class="ri-search-line absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          </div>
          <button 
            onclick="startAnalysis()" 
            id="analyzeBtn"
            class="px-6 py-3 bg-primary text-white rounded-lg font-medium hover:bg-blue-600 transition-colors"
          >
            开始分析
          </button>
        </div>
      </div>
    </section>

    <!-- 分析状态 -->
    <section id="analysisStatus" class="mb-6 hidden">
      <div class="bg-white rounded-xl p-6 shadow-sm text-center">
        <div class="analyzing mb-4">
          <i class="ri-robot-line text-4xl text-primary"></i>
        </div>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">AI正在分析中</h3>
        <p class="text-sm text-gray-600 loading-dots">正在获取股票数据和市场信息</p>
        <div class="mt-4 bg-gray-200 rounded-full h-2">
          <div id="progressBar" class="bg-primary h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
        </div>
      </div>
    </section>

    <!-- 分析结果 -->
    <section id="analysisResult" class="hidden">
      <!-- 股票基本信息 -->
      <div class="bg-white rounded-xl p-4 shadow-sm mb-4">
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center space-x-3">
            <h2 id="stockName" class="text-lg font-bold text-gray-900">-</h2>
            <span id="stockCode" class="text-sm text-gray-500">-</span>
          </div>
          <div class="text-right">
            <div id="stockPrice" class="text-xl font-bold text-gray-900">¥-</div>
            <div id="stockChange" class="text-sm">-</div>
          </div>
        </div>
      </div>

      <!-- AI投资建议 -->
      <div class="bg-white rounded-xl p-4 shadow-sm mb-4">
        <h3 class="font-semibold text-gray-900 mb-3 flex items-center">
          <i class="ri-lightbulb-line text-green-600 mr-2"></i>
          AI投资建议
        </h3>
        <div id="investmentAdvice" class="space-y-3">
          <!-- 动态生成内容 -->
        </div>
      </div>

      <!-- 基本面分析 -->
      <div class="bg-white rounded-xl p-4 shadow-sm mb-4">
        <h3 class="font-semibold text-gray-900 mb-3 flex items-center">
          <i class="ri-building-line text-blue-600 mr-2"></i>
          基本面分析
        </h3>
        <div id="fundamentalAnalysis" class="space-y-2">
          <!-- 动态生成内容 -->
        </div>
      </div>

      <!-- 技术面分析 -->
      <div class="bg-white rounded-xl p-4 shadow-sm mb-4">
        <h3 class="font-semibold text-gray-900 mb-3 flex items-center">
          <i class="ri-line-chart-line text-purple-600 mr-2"></i>
          技术面分析
        </h3>
        <div id="technicalAnalysis" class="space-y-2">
          <!-- 动态生成内容 -->
        </div>
      </div>

      <!-- 市场情绪 -->
      <div class="bg-white rounded-xl p-4 shadow-sm mb-4">
        <h3 class="font-semibold text-gray-900 mb-3 flex items-center">
          <i class="ri-emotion-line text-yellow-600 mr-2"></i>
          市场情绪
        </h3>
        <div id="marketSentiment" class="space-y-2">
          <!-- 动态生成内容 -->
        </div>
      </div>

      <!-- 操作建议 -->
      <div class="bg-white rounded-xl p-4 shadow-sm mb-4">
        <h3 class="font-semibold text-gray-900 mb-3 flex items-center">
          <i class="ri-settings-line text-gray-600 mr-2"></i>
          操作建议
        </h3>
        <div class="grid grid-cols-2 gap-3">
          <button onclick="addToWatchlist()" class="flex items-center justify-center space-x-2 py-3 border border-primary text-primary rounded-lg hover:bg-blue-50">
            <i class="ri-star-line"></i>
            <span>加入自选</span>
          </button>
          <button onclick="openSimulation()" class="flex items-center justify-center space-x-2 py-3 bg-primary text-white rounded-lg hover:bg-blue-600">
            <i class="ri-robot-line"></i>
            <span>模拟交易</span>
          </button>
        </div>
      </div>
    </section>

    <!-- 历史搜索 -->
    <section id="searchHistory" class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <h3 class="font-semibold text-gray-900 mb-3">最近搜索</h3>
        <div class="flex flex-wrap gap-2" id="historyList">
          <button onclick="quickSearch('600519', '贵州茅台')" class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200">
            贵州茅台
          </button>
          <button onclick="quickSearch('002594', '比亚迪')" class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200">
            比亚迪
          </button>
          <button onclick="quickSearch('300750', '宁德时代')" class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200">
            宁德时代
          </button>
        </div>
      </div>
    </section>
  </main>

  <!-- 底部导航栏 -->
  <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
    <div class="grid grid-cols-4 py-2">
      <a href="index.html" class="flex flex-col items-center py-2 text-gray-600">
        <i class="ri-home-line text-xl mb-1"></i>
        <span class="text-xs">首页</span>
      </a>
      <a href="watchlist.html" class="flex flex-col items-center py-2 text-gray-600">
        <i class="ri-star-line text-xl mb-1"></i>
        <span class="text-xs">自选股</span>
      </a>
      <a href="reports.html" class="flex flex-col items-center py-2 text-gray-600">
        <i class="ri-file-text-line text-xl mb-1"></i>
        <span class="text-xs">报告中心</span>
      </a>
      <a href="关于我们.html" class="flex flex-col items-center py-2 text-gray-600">
        <i class="ri-user-line text-xl mb-1"></i>
        <span class="text-xs">我的</span>
      </a>
    </div>
  </nav>

  <script>
    // 股票数据库
    const stockDatabase = {
      '600519': { name: '贵州茅台', price: 1678.50, change: '*****%', changeAmount: '+38.50' },
      '002594': { name: '比亚迪', price: 245.80, change: '*****%', changeAmount: '*****' },
      '300750': { name: '宁德时代', price: 185.20, change: '-1.85%', changeAmount: '-3.50' },
      '601318': { name: '中国平安', price: 42.50, change: '-0.93%', changeAmount: '-0.40' }
    };

    let currentStock = null;

    // 开始分析
    function startAnalysis() {
      const input = document.getElementById('stockInput').value.trim();
      if (!input) {
        alert('请输入股票代码或名称');
        return;
      }

      // 查找股票
      const stock = findStock(input);
      if (!stock) {
        alert('未找到该股票，请检查输入');
        return;
      }

      currentStock = stock;
      showAnalysisStatus();
      simulateAnalysis();
    }

    // 查找股票
    function findStock(input) {
      for (const [code, data] of Object.entries(stockDatabase)) {
        if (code === input || data.name.includes(input)) {
          return { code, ...data };
        }
      }
      return null;
    }

    // 显示分析状态
    function showAnalysisStatus() {
      document.getElementById('searchHistory').classList.add('hidden');
      document.getElementById('analysisResult').classList.add('hidden');
      document.getElementById('analysisStatus').classList.remove('hidden');
    }

    // 模拟分析过程
    function simulateAnalysis() {
      const progressBar = document.getElementById('progressBar');
      const statusText = document.querySelector('.loading-dots');
      
      const steps = [
        { progress: 20, text: '正在获取股票数据' },
        { progress: 40, text: '正在分析技术指标' },
        { progress: 60, text: '正在分析基本面' },
        { progress: 80, text: '正在分析市场情绪' },
        { progress: 100, text: '分析完成' }
      ];

      let currentStep = 0;
      const interval = setInterval(() => {
        if (currentStep < steps.length) {
          const step = steps[currentStep];
          progressBar.style.width = step.progress + '%';
          statusText.textContent = step.text;
          currentStep++;
        } else {
          clearInterval(interval);
          setTimeout(showAnalysisResult, 500);
        }
      }, 800);
    }

    // 显示分析结果
    function showAnalysisResult() {
      document.getElementById('analysisStatus').classList.add('hidden');
      document.getElementById('analysisResult').classList.remove('hidden');
      
      // 填充股票基本信息
      document.getElementById('stockName').textContent = currentStock.name;
      document.getElementById('stockCode').textContent = currentStock.code;
      document.getElementById('stockPrice').textContent = `¥${currentStock.price}`;
      
      const changeElement = document.getElementById('stockChange');
      changeElement.textContent = `${currentStock.change} (${currentStock.changeAmount})`;
      changeElement.className = currentStock.change.startsWith('+') ? 'text-sm text-red-500' : 'text-sm text-green-500';

      // 生成分析内容
      generateAnalysisContent();
    }

    // 生成分析内容
    function generateAnalysisContent() {
      // AI投资建议
      const advice = getInvestmentAdvice(currentStock.code);
      document.getElementById('investmentAdvice').innerHTML = advice;

      // 基本面分析
      const fundamental = getFundamentalAnalysis(currentStock.code);
      document.getElementById('fundamentalAnalysis').innerHTML = fundamental;

      // 技术面分析
      const technical = getTechnicalAnalysis(currentStock.code);
      document.getElementById('technicalAnalysis').innerHTML = technical;

      // 市场情绪
      const sentiment = getMarketSentiment(currentStock.code);
      document.getElementById('marketSentiment').innerHTML = sentiment;
    }

    // 获取投资建议
    function getInvestmentAdvice(code) {
      const adviceData = {
        '600519': {
          action: '持有',
          color: 'green',
          target: '¥1,750-1,800',
          stopLoss: '¥1,600',
          position: '建议仓位20-30%'
        },
        '002594': {
          action: '买入',
          color: 'green',
          target: '¥280-300',
          stopLoss: '¥220',
          position: '建议仓位15-25%'
        },
        '300750': {
          action: '观望',
          color: 'yellow',
          target: '¥200-220',
          stopLoss: '¥170',
          position: '建议仓位10-15%'
        },
        '601318': {
          action: '减仓',
          color: 'red',
          target: '¥45-48',
          stopLoss: '¥38',
          position: '建议仓位5-10%'
        }
      };

      const data = adviceData[code] || adviceData['600519'];
      return `
        <div class="bg-${data.color}-50 rounded-lg p-3 border border-${data.color}-200">
          <div class="flex items-center justify-between mb-2">
            <span class="text-lg font-bold text-${data.color}-600">${data.action}</span>
            <span class="text-xs bg-${data.color}-100 text-${data.color}-700 px-2 py-1 rounded">推荐</span>
          </div>
          <div class="text-sm text-gray-700 space-y-1">
            <div>目标价位：<span class="font-medium">${data.target}</span></div>
            <div>止损位：<span class="font-medium text-red-600">${data.stopLoss}</span></div>
            <div>${data.position}</div>
          </div>
        </div>
      `;
    }

    // 获取基本面分析
    function getFundamentalAnalysis(code) {
      const fundamentalData = {
        '600519': [
          '三季度业绩超预期，ROE达到25.8%',
          '白酒行业景气度回升，春节备货需求旺盛',
          '品牌护城河深厚，定价权强',
          '现金流充沛，分红稳定'
        ],
        '002594': [
          '新能源汽车销量持续增长',
          '电池技术领先，产业链完整',
          '海外市场拓展顺利',
          '政策支持力度大'
        ],
        '300750': [
          '电池技术全球领先',
          '客户结构优质，绑定头部车企',
          '产能扩张积极，全球布局',
          '面临原材料成本压力'
        ],
        '601318': [
          '保险业务稳健增长',
          '投资收益承压',
          '估值处于历史低位',
          '分红收益率较高'
        ]
      };

      const data = fundamentalData[code] || fundamentalData['600519'];
      return data.map(item => `
        <div class="flex items-center">
          <span class="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
          <span class="text-sm text-gray-700">${item}</span>
        </div>
      `).join('');
    }

    // 获取技术面分析
    function getTechnicalAnalysis(code) {
      const technicalData = {
        '600519': [
          'MACD金叉，多头排列',
          'RSI指标显示超买信号，注意短期回调',
          '成交量放大，资金关注度提升',
          '突破前期高点，趋势向好'
        ],
        '002594': [
          '突破关键阻力位250元',
          '均线多头排列，趋势强劲',
          '成交量持续放大',
          'MACD指标向上发散'
        ],
        '300750': [
          '在180-200区间震荡',
          '等待方向选择',
          '支撑位有效',
          '成交量萎缩'
        ],
        '601318': [
          'RSI进入超卖区间',
          '下跌趋势未改变',
          '需关注40元支撑',
          '成交量低迷'
        ]
      };

      const data = technicalData[code] || technicalData['600519'];
      return data.map(item => `
        <div class="flex items-center">
          <span class="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
          <span class="text-sm text-gray-700">${item}</span>
        </div>
      `).join('');
    }

    // 获取市场情绪
    function getMarketSentiment(code) {
      const sentimentData = {
        '600519': [
          '机构资金持续流入，北向资金净买入',
          '白酒板块整体表现强势',
          '市场对春节行情预期乐观'
        ],
        '002594': [
          '新能源汽车板块受到追捧',
          '机构调研频繁，关注度高',
          '政策利好不断释放'
        ],
        '300750': [
          '资金观望情绪浓厚',
          '等待业绩指引',
          '机构分歧较大'
        ],
        '601318': [
          '保险板块整体低迷',
          '资金流出明显',
          '估值修复需要时间'
        ]
      };

      const data = sentimentData[code] || sentimentData['600519'];
      return data.map(item => `
        <div class="flex items-center">
          <span class="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
          <span class="text-sm text-gray-700">${item}</span>
        </div>
      `).join('');
    }

    // 快速搜索
    function quickSearch(code, name) {
      document.getElementById('stockInput').value = name;
      startAnalysis();
    }

    // 回车键搜索
    function handleEnterKey(event) {
      if (event.key === 'Enter') {
        startAnalysis();
      }
    }

    // 加入自选股
    function addToWatchlist() {
      if (!currentStock) {
        alert('请先搜索股票');
        return;
      }
      alert(`${currentStock.name} 已添加到自选股`);
    }

    // 打开模拟交易
    function openSimulation() {
      window.location.href = '模拟盘.html';
    }

    // 返回功能
    function goBack() {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        window.location.href = 'index.html';
      }
    }

    // 页面加载时检查URL参数
    document.addEventListener('DOMContentLoaded', function() {
      const urlParams = new URLSearchParams(window.location.search);
      const stockParam = urlParams.get('stock');

      if (stockParam) {
        document.getElementById('stockInput').value = stockParam;
        startAnalysis();
      }
    });
  </script>
</body>
</html>
