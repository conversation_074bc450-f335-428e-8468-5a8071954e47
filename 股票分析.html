<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>股票分析 - 贵州茅台</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: "#3B82F6",
            secondary: "#F1F5F9",
          },
        },
      },
    };
  </script>
  <style>
    .stock-up { color: #EF4444; }
    .stock-down { color: #10B981; }
    .gradient-card { 
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .ai-badge {
      background: linear-gradient(45deg, #667eea, #764ba2);
      animation: pulse 2s infinite;
    }
  </style>
</head>
<body class="bg-gray-50 min-h-screen">
  <!-- 顶部导航栏 -->
  <nav class="fixed top-0 w-full bg-white z-50 px-4 py-3 border-b border-gray-100">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <button onclick="goBack()" class="w-8 h-8 flex items-center justify-center mr-3 cursor-pointer">
          <i class="ri-arrow-left-line text-gray-600 text-lg"></i>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">股票分析</h1>
      </div>
      <div class="flex items-center space-x-2">
        <!-- 搜索按钮 -->
        <button onclick="openSearch()" class="w-8 h-8 flex items-center justify-center">
          <i class="ri-search-line text-gray-600 text-lg"></i>
        </button>
        <!-- 消息中心按钮 -->
        <button onclick="openMessageCenter()" class="w-8 h-8 flex items-center justify-center relative">
          <i class="ri-notification-line text-gray-600 text-lg"></i>
          <!-- 未读消息提示点 -->
          <div class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
        </button>
        <!-- 微信小程序分享按钮 -->
        <button onclick="shareApp()" class="w-8 h-8 flex items-center justify-center">
          <div class="flex space-x-0.5">
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
          </div>
        </button>
        <!-- 微信小程序返回主界面按钮 -->
        <button class="w-8 h-8 flex items-center justify-center">
          <div class="w-5 h-5 border-2 border-gray-600 rounded-full flex items-center justify-center">
            <div class="w-2 h-2 bg-gray-600 rounded-full"></div>
          </div>
        </button>
      </div>
    </div>
  </nav>

  <!-- 主要内容区域 -->
  <main class="pt-20 pb-20 px-4">
    <!-- 股价信息 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="flex items-center justify-between mb-4">
          <div>
            <div class="text-2xl font-bold text-gray-900">¥1,678.50</div>
            <div class="flex items-center space-x-2 mt-1">
              <span class="stock-up text-sm font-medium">+39.50</span>
              <span class="stock-up text-sm font-medium">+2.35%</span>
              <span class="text-xs text-gray-500">今日</span>
            </div>
          </div>
          <div class="text-right text-xs text-gray-500">
            <div>更新时间</div>
            <div>15:00:00</div>
          </div>
        </div>
        
        <!-- 快速操作 -->
        <div class="grid grid-cols-3 gap-3">
          <button id="add-to-watchlist" class="px-4 py-2 bg-primary text-white rounded-lg text-sm">
            <i class="ri-star-line mr-1"></i>加自选
          </button>
          <button id="view-simulation" class="px-4 py-2 bg-green-500 text-white rounded-lg text-sm">
            <i class="ri-line-chart-line mr-1"></i>模拟盘
          </button>
          <button id="generate-report" class="px-4 py-2 bg-purple-500 text-white rounded-lg text-sm">
            <i class="ri-file-text-line mr-1"></i>生成报告
          </button>
        </div>
      </div>
    </section>



    

    <!-- 功能标签页 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl shadow-sm overflow-hidden">
        <div class="flex border-b">
          <button class="tab-btn active flex-1 py-3 text-sm font-medium text-primary border-b-2 border-primary">
            实时分析
          </button>
          <button class="tab-btn flex-1 py-3 text-sm font-medium text-gray-500">
            历史报告
          </button>
          <button class="tab-btn flex-1 py-3 text-sm font-medium text-gray-500">
            模拟交易
          </button>
        </div>

        <!-- 实时分析内容 -->
        <div id="realtime-analysis" class="tab-content p-4">
          <div class="space-y-4">
            <!-- AI投资建议 - 最重要，放在最前面 -->
            <div class="p-4 bg-green-50 rounded-lg border border-green-200">
              <div class="flex items-center mb-3">
                <i class="ri-lightbulb-line text-green-600 mr-2"></i>
                <span class="font-medium text-gray-900">AI投资建议</span>
                <span class="text-xs text-gray-500 ml-auto">刚刚更新</span>
              </div>
              <div class="bg-white rounded-lg p-3 border-l-4 border-green-500">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-lg font-bold text-green-600">持有</span>
                  <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">推荐</span>
                </div>
                <div class="text-sm text-gray-700 space-y-1">
                  <div>目标价位：<span class="font-medium">¥1,750-1,800</span></div>
                  <div>止损位：<span class="font-medium text-red-600">¥1,600</span></div>
                  <div>建议仓位：<span class="font-medium">29.0%</span></div>
                </div>
              </div>
            </div>

            <!-- 基本面分析 - 第二重要 -->
            <div class="p-4 bg-blue-50 rounded-lg">
              <div class="flex items-center mb-2">
                <i class="ri-building-line text-blue-600 mr-2"></i>
                <span class="font-medium text-gray-900">基本面分析</span>
              </div>
              <div class="space-y-2 text-sm">
                <div class="flex items-center">
                  <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  <span>三季度业绩超预期，ROE达到25.8%</span>
                </div>
                <div class="flex items-center">
                  <span class="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  <span>白酒行业景气度回升，春节备货需求旺盛</span>
                </div>
                <div class="flex items-center">
                  <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  <span>品牌护城河深厚，定价权强</span>
                </div>
              </div>
            </div>

            <!-- 技术面分析 - 第三重要 -->
            <div class="p-4 bg-purple-50 rounded-lg">
              <div class="flex items-center mb-2">
                <i class="ri-line-chart-line text-purple-600 mr-2"></i>
                <span class="font-medium text-gray-900">技术面分析</span>
              </div>
              <div class="space-y-2 text-sm">
                <div class="flex items-center">
                  <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  <span>MACD金叉，多头排列</span>
                </div>
                <div class="flex items-center">
                  <span class="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                  <span>RSI指标显示超买信号，注意短期回调</span>
                </div>
                <div class="flex items-center">
                  <span class="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  <span>成交量放大，资金关注度提升</span>
                </div>
              </div>
            </div>

            <!-- 市场情绪 -->
            <div class="p-4 bg-yellow-50 rounded-lg">
              <div class="flex items-center mb-2">
                <i class="ri-emotion-line text-yellow-600 mr-2"></i>
                <span class="font-medium text-gray-900">市场情绪</span>
              </div>
              <div class="space-y-2 text-sm">
                <div class="flex items-center">
                  <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  <span>机构资金持续流入，北向资金净买入</span>
                </div>
                <div class="flex items-center">
                  <span class="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  <span>白酒板块整体表现强势</span>
                </div>
              </div>
            </div>

            <!-- 风险提示 -->
            <div class="p-4 bg-red-50 rounded-lg border border-red-200">
              <div class="flex items-center mb-2">
                <i class="ri-alert-line text-red-600 mr-2"></i>
                <span class="font-medium text-gray-900">风险提示</span>
              </div>
              <div class="text-sm text-red-700">
                当前估值偏高，短期存在回调风险。建议控制仓位，分批建仓。
              </div>
            </div>
          </div>
        </div>

        <!-- 历史报告内容 -->
        <div id="history-reports" class="tab-content p-4 hidden">
          <div class="space-y-3">
            <div class="flex items-center justify-between mb-4">
              <h4 class="font-medium text-gray-900">历史分析报告</h4>
              <select class="text-sm border border-gray-200 rounded px-2 py-1">
                <option>最近30天</option>
                <option>最近7天</option>
                <option>最近3个月</option>
              </select>
            </div>
            
            <!-- 报告列表 -->
            <div class="space-y-3">
              <div class="report-item p-3 border border-gray-200 rounded-lg cursor-pointer hover:border-primary">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-gray-900">2024-01-15 分析报告</span>
                  <div class="flex items-center space-x-2">
                    <span class="text-xs text-gray-500">买入建议</span>
                  </div>
                </div>
                <p class="text-xs text-gray-600 mb-2">
                  预测价格区间：¥1,650-1,700，实际收盘：¥1,678.50
                </p>
                <div class="flex items-center text-xs text-gray-500">
                  <i class="ri-time-line mr-1"></i>
                  <span>生成时间：2024-01-15 09:30</span>
                </div>
              </div>

              <div class="report-item p-3 border border-gray-200 rounded-lg cursor-pointer hover:border-primary">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-gray-900">2024-01-14 分析报告</span>
                  <div class="flex items-center space-x-2">
                    <span class="text-xs text-gray-500">持有建议</span>
                  </div>
                </div>
                <p class="text-xs text-gray-600 mb-2">
                  预测价格区间：¥1,620-1,660，实际收盘：¥1,639.00
                </p>
                <div class="flex items-center text-xs text-gray-500">
                  <i class="ri-time-line mr-1"></i>
                  <span>生成时间：2024-01-14 09:30</span>
                </div>
              </div>

              <div class="report-item p-3 border border-gray-200 rounded-lg cursor-pointer hover:border-primary">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-gray-900">2024-01-13 分析报告</span>
                  <div class="flex items-center space-x-2">
                    <span class="text-xs text-gray-500">卖出建议</span>
                  </div>
                </div>
                <p class="text-xs text-gray-600 mb-2">
                  预测价格区间：¥1,580-1,620，实际收盘：¥1,639.00
                </p>
                <div class="flex items-center text-xs text-gray-500">
                  <i class="ri-time-line mr-1"></i>
                  <span>生成时间：2024-01-13 09:30</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 模拟交易内容 -->
        <div id="simulation-trading" class="tab-content p-4 hidden">
          <div class="space-y-4">
            <!-- 模拟盘概况 -->
            <div class="p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
              <div class="flex items-center justify-between mb-3">
                <h4 class="font-medium text-gray-900">模拟盘表现</h4>
                <span class="text-xs bg-white px-2 py-1 rounded text-gray-600">基于AI建议</span>
              </div>
              <div class="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div class="text-lg font-bold text-green-600">+12.5%</div>
                  <div class="text-xs text-gray-500">总收益率</div>
                </div>
                <div>
                  <div class="text-lg font-bold text-gray-900">¥112,500</div>
                  <div class="text-xs text-gray-500">当前市值</div>
                </div>
                <div>
                  <div class="text-lg font-bold text-blue-600">15天</div>
                  <div class="text-xs text-gray-500">持有天数</div>
                </div>
              </div>
            </div>

            <!-- 交易记录 -->
            <div>
              <h4 class="font-medium text-gray-900 mb-3">交易记录</h4>
              <div class="space-y-2">
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div class="text-sm font-medium text-gray-900">买入 100股</div>
                    <div class="text-xs text-gray-500">2024-01-01 09:30</div>
                  </div>
                  <div class="text-right">
                    <div class="text-sm font-medium text-gray-900">¥1,600.00</div>
                    <div class="text-xs text-green-600">AI建议执行</div>
                  </div>
                </div>
                
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div class="text-sm font-medium text-gray-900">加仓 50股</div>
                    <div class="text-xs text-gray-500">2024-01-08 10:15</div>
                  </div>
                  <div class="text-right">
                    <div class="text-sm font-medium text-gray-900">¥1,650.00</div>
                    <div class="text-xs text-green-600">AI建议执行</div>
                  </div>
                </div>
              </div>

              <!-- 进入个股模拟盘按钮 -->
              <div class="mt-4 text-center">
                <button id="enter-simulation" class="px-6 py-2 bg-primary text-white rounded-lg text-sm font-medium hover:bg-blue-600 transition-colors">
                  <i class="ri-line-chart-line mr-1"></i>进入个股模拟盘
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 会员升级提示 -->
    <section class="mb-6">
      <div class="gradient-card rounded-xl p-4 text-white">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="font-semibold mb-1">升级会员解锁更多功能</h3>
            <p class="text-sm opacity-90">每日报告、高级AI引擎、无限关注</p>
          </div>
          <button onclick="window.location.href='会员升级.html'" id="upgrade-btn" class="bg-white text-purple-600 px-4 py-2 rounded-lg text-sm font-medium">
            立即升级
          </button>
        </div>
      </div>
    </section>
  </main>

  <!-- 底部导航栏 -->
  <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
    <div class="grid grid-cols-4 h-16">
      <button onclick="window.location.href='index.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-apps-2-line text-lg text-gray-500"></i>
        </div>
        <span class="text-xs text-gray-500">首页</span>
      </button>
      <button onclick="window.location.href='自选股.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item active">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-star-line text-lg text-primary"></i>
        </div>
        <span class="text-xs text-primary">自选股</span>
      </button>
      <button onclick="window.location.href='reports.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-file-list-line text-lg text-gray-500"></i>
        </div>
        <span class="text-xs text-gray-500">报告中心</span>
      </button>
      <button onclick="window.location.href='我的.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-user-line text-lg text-gray-500"></i>
        </div>
        <span class="text-xs text-gray-500">我的</span>
      </button>
    </div>
  </nav>

  <script>
    // 股票数据库（模拟）
    const stockDatabase = {
      '600519': {
        name: '贵州茅台',
        price: '1678.50',
        change: '+2.35%',
        changeAmount: '+39.50',
        market: 'A股',
        industry: '白酒'
      },
      '000858': {
        name: '五粮液',
        price: '158.20',
        change: '+1.85%',
        changeAmount: '+2.87',
        market: 'A股',
        industry: '白酒'
      },
      '002594': {
        name: '比亚迪',
        price: '245.80',
        change: '+3.25%',
        changeAmount: '+7.72',
        market: 'A股',
        industry: '新能源汽车'
      },
      '300750': {
        name: '宁德时代',
        price: '185.20',
        change: '-0.65%',
        changeAmount: '-1.21',
        market: 'A股',
        industry: '电池'
      }
    };

    document.addEventListener("DOMContentLoaded", function () {
      // 获取URL参数
      const urlParams = new URLSearchParams(window.location.search);
      const stockCode = urlParams.get('code');
      const stockName = urlParams.get('name');

      // 如果有股票代码参数，更新页面信息
      if (stockCode && stockDatabase[stockCode]) {
        updateStockInfo(stockCode, stockDatabase[stockCode]);
      }

      // 检查是否需要显示特定标签页
      const tab = urlParams.get('tab');
      if (tab === 'history') {
        // 切换到历史报告标签
        document.querySelectorAll('.tab-btn')[1].click();
      }

      // 标签页切换
      const tabBtns = document.querySelectorAll('.tab-btn');
      const tabContents = document.querySelectorAll('.tab-content');

      tabBtns.forEach((btn, index) => {
        btn.addEventListener('click', function() {
          // 移除所有活动状态
          tabBtns.forEach(b => {
            b.classList.remove('active', 'text-primary', 'border-primary');
            b.classList.add('text-gray-500');
          });
          tabContents.forEach(content => content.classList.add('hidden'));

          // 添加当前活动状态
          this.classList.add('active', 'text-primary', 'border-primary');
          this.classList.remove('text-gray-500');
          tabContents[index].classList.remove('hidden');
        });
      });

      // AI引擎选择
      const aiEngines = document.querySelectorAll('.ai-engine');
      aiEngines.forEach(engine => {
        engine.addEventListener('click', function() {
          aiEngines.forEach(e => {
            e.classList.remove('active', 'border-primary', 'bg-blue-50');
            e.classList.add('border-gray-200', 'bg-white');
          });
          this.classList.add('active', 'border-primary', 'bg-blue-50');
          this.classList.remove('border-gray-200', 'bg-white');
        });
      });

      // 快速操作按钮
      document.getElementById('add-to-watchlist').addEventListener('click', function() {
        this.innerHTML = '<i class="ri-star-fill mr-1"></i>已添加';
        this.classList.remove('bg-primary');
        this.classList.add('bg-gray-400');
      });

      document.getElementById('view-simulation').addEventListener('click', function() {
        // 切换到模拟交易标签
        tabBtns[2].click();
      });

      // 进入个股模拟盘
      document.getElementById('enter-simulation').addEventListener('click', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const stockCode = urlParams.get('code') || '600519';
        const stockName = urlParams.get('name') || '贵州茅台';

        // 跳转到个股模拟盘页面
        const url = `个股模拟盘.html?code=${stockCode}&name=${encodeURIComponent(stockName)}`;
        window.location.href = url;
      });

      document.getElementById('generate-report').addEventListener('click', function() {
        this.innerHTML = '<i class="ri-loader-4-line mr-1 animate-spin"></i>生成中...';
        setTimeout(() => {
          this.innerHTML = '<i class="ri-file-text-line mr-1"></i>查看报告';
          // 切换到历史报告标签
          tabBtns[1].click();
        }, 2000);
      });

      // 报告点击事件
      document.querySelectorAll('.report-item').forEach((item, index) => {
        item.addEventListener('click', function() {
          // 获取当前股票信息
          const urlParams = new URLSearchParams(window.location.search);
          const stockCode = urlParams.get('code') || '600519';
          const stockName = urlParams.get('name') || '贵州茅台';

          // 根据索引生成不同的报告日期
          const dates = ['2024-01-15', '2024-01-14', '2024-01-13'];
          const reportDate = dates[index] || '2024-01-15';

          // 跳转到详细报告页面
          const reportUrl = `详细报告.html?code=${stockCode}&name=${encodeURIComponent(stockName)}&type=daily&date=${reportDate}`;
          window.location.href = reportUrl;
        });
      });

      // 底部导航栏交互
      const tabItems = document.querySelectorAll(".tab-item");
      tabItems.forEach((item) => {
        item.addEventListener("click", function () {
          tabItems.forEach((tab) => {
            const icon = tab.querySelector("i");
            const text = tab.querySelector("span");
            icon.classList.remove("text-primary");
            icon.classList.add("text-gray-500");
            text.classList.remove("text-primary", "font-medium");
            text.classList.add("text-gray-500");
            tab.classList.remove("active");
          });
          const icon = this.querySelector("i");
          const text = this.querySelector("span");
          icon.classList.remove("text-gray-500");
          icon.classList.add("text-primary");
          text.classList.remove("text-gray-500");
          text.classList.add("text-primary", "font-medium");
          this.classList.add("active");
        });
      });
    });

    // 跳转到板块详情页面
    function goToSector(sectorName, sectorCode) {
      // 可以传递板块信息到详情页面
      const url = `板块详情.html?name=${encodeURIComponent(sectorName)}&code=${sectorCode}`;
      window.location.href = url;
    }

    // 更新股票信息函数
    function updateStockInfo(stockCode, stockInfo) {
      // 更新页面标题
      document.title = `股票分析 - ${stockInfo.name}`;

      // 更新导航栏标题
      document.querySelector('nav h1').textContent = `${stockInfo.name} (${stockCode})`;

      // 更新股价信息
      document.querySelector('.text-2xl.font-bold').textContent = `¥${stockInfo.price}`;

      // 更新涨跌信息
      const changeElements = document.querySelectorAll('.stock-up');
      if (changeElements.length >= 2) {
        changeElements[0].textContent = stockInfo.changeAmount;
        changeElements[1].textContent = stockInfo.change;

        // 根据涨跌设置颜色
        const isUp = stockInfo.change.startsWith('+');
        changeElements.forEach(el => {
          el.className = isUp ? 'stock-up text-sm font-medium' : 'stock-down text-sm font-medium';
        });
      }

      // 更新AI分析内容中的股票名称
      const analysisText = document.querySelector('#realtime-analysis p');
      if (analysisText) {
        analysisText.textContent = `基于当前市场数据和技术指标分析，${stockInfo.name}呈现以下特征：`;
      }
    }

    // 返回功能
    function goBack() {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        window.location.href = 'index.html';
      }
    }

    // 打开搜索页面
    function openSearch() {
      window.location.href = 'search.html';
    }

    // 打开消息中心
    function openMessageCenter() {
      window.location.href = '消息中心.html';
    }

    // 分享功能
    function shareApp() {
      if (navigator.share) {
        navigator.share({
          title: '金融智能体 - 股票分析',
          text: '查看股票详细分析',
          url: window.location.href
        }).catch(err => {
          console.log('分享失败:', err);
        });
      } else {
        if (navigator.clipboard) {
          navigator.clipboard.writeText(window.location.href).then(() => {
            alert('链接已复制到剪贴板');
          });
        }
      }
    }
  </script>
</body>
</html>
