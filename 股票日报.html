<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>股票日报 - 金融智能体</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
  <style>
    .primary { color: #3b82f6; }
    .bg-primary { background-color: #3b82f6; }
    .text-primary { color: #3b82f6; }
    .border-primary { border-color: #3b82f6; }
    .tab-btn.active {
      color: #3b82f6;
      border-bottom-color: #3b82f6;
    }
    .tab-content.hidden {
      display: none;
    }
    .gradient-bg {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  </style>
</head>
<body class="bg-gray-50">
  <!-- 顶部导航栏 -->
  <header class="bg-white shadow-sm fixed top-0 left-0 right-0 z-50">
    <div class="flex items-center justify-between px-4 py-3">
      <div class="flex items-center space-x-3">
        <button onclick="goBack()" class="p-2 hover:bg-gray-100 rounded-lg">
          <i class="ri-arrow-left-line text-xl text-gray-600"></i>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">股票日报</h1>
      </div>
      <div class="flex items-center space-x-2">
        <button onclick="addToWatchlist()" class="p-2 hover:bg-gray-100 rounded-lg">
          <i class="ri-star-line text-xl text-gray-600"></i>
        </button>
        <button onclick="shareReport()" class="p-2 hover:bg-gray-100 rounded-lg">
          <i class="ri-share-line text-xl text-gray-600"></i>
        </button>
      </div>
    </div>
  </header>

  <!-- 主要内容区域 -->
  <main class="pt-16 pb-20 px-4">
    <!-- 股票基本信息 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center space-x-3">
            <h2 id="stockName" class="text-xl font-bold text-gray-900">贵州茅台</h2>
            <span id="stockCode" class="text-sm text-gray-500">600519</span>
          </div>
          <div class="text-right">
            <div id="stockPrice" class="text-2xl font-bold text-gray-900">¥1,678.50</div>
            <div id="stockChange" class="text-sm text-red-500">+2.35% (+¥38.50)</div>
          </div>
        </div>
        <div class="grid grid-cols-4 gap-3 text-center">
          <div>
            <div class="text-xs text-gray-500">开盘</div>
            <div class="text-sm font-medium">¥1,645.00</div>
          </div>
          <div>
            <div class="text-xs text-gray-500">最高</div>
            <div class="text-sm font-medium">¥1,685.20</div>
          </div>
          <div>
            <div class="text-xs text-gray-500">最低</div>
            <div class="text-sm font-medium">¥1,638.80</div>
          </div>
          <div>
            <div class="text-xs text-gray-500">成交量</div>
            <div class="text-sm font-medium">1.2万手</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 报告类型切换 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="flex items-center justify-between mb-3">
          <h4 class="font-medium text-gray-900">分析报告</h4>
          <div class="flex items-center space-x-2">
            <select id="reportTypeSelector" onchange="switchReportType()" class="bg-gray-50 border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary">
              <option value="daily" selected>日报</option>
              <option value="weekly">周报</option>
            </select>
          </div>
        </div>
        <div class="text-sm text-gray-600">
          <span id="report-description">查看该股票的详细技术分析和短期操作建议</span>
        </div>
      </div>
    </section>

    <!-- 日报标签页 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl shadow-sm overflow-hidden">
        <div class="flex border-b">
          <button class="tab-btn active flex-1 py-3 text-sm font-medium text-primary border-b-2 border-primary" onclick="switchTab('morning')">
            <i class="ri-sun-line mr-1"></i>盘前报告
          </button>
          <button class="tab-btn flex-1 py-3 text-sm font-medium text-gray-500" onclick="switchTab('evening')">
            <i class="ri-moon-line mr-1"></i>盘后总结
          </button>
        </div>

        <!-- 盘前报告内容 -->
        <div id="morning-report" class="tab-content p-4">
          <div class="space-y-4">
            <!-- 报告时间 -->
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center text-sm text-gray-600">
                <i class="ri-time-line mr-1"></i>
                <span>2024年1月15日 09:00 盘前分析</span>
              </div>
            </div>

            <!-- AI投资建议 -->
            <div class="p-4 bg-green-50 rounded-lg border border-green-200">
              <div class="flex items-center mb-3">
                <i class="ri-lightbulb-line text-green-600 mr-2"></i>
                <span class="font-medium text-gray-900">今日AI建议</span>
              </div>
              <div class="bg-white rounded-lg p-3 border-l-4 border-green-500">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-lg font-bold text-green-600">关注买入</span>
                  <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">推荐</span>
                </div>
                <div class="text-sm text-gray-700 space-y-1">
                  <div>建议买入区间：<span class="font-medium">¥1,620-1,650</span></div>
                  <div>目标价位：<span class="font-medium">¥1,750-1,800</span></div>
                  <div>止损位：<span class="font-medium text-red-600">¥1,600</span></div>
                </div>
              </div>
            </div>

            <!-- 今日关注点 -->
            <div class="p-4 bg-blue-50 rounded-lg">
              <div class="flex items-center mb-3">
                <i class="ri-focus-line text-blue-600 mr-2"></i>
                <span class="font-medium text-gray-900">今日关注点</span>
              </div>
              <div class="space-y-2 text-sm">
                <div class="flex items-start">
                  <span class="w-2 h-2 bg-red-500 rounded-full mr-2 mt-2"></span>
                  <span><strong>重要支撑：</strong>关注¥1,620支撑位，跌破需谨慎</span>
                </div>
                <div class="flex items-start">
                  <span class="w-2 h-2 bg-green-500 rounded-full mr-2 mt-2"></span>
                  <span><strong>突破目标：</strong>突破¥1,680阻力位，有望挑战新高</span>
                </div>
                <div class="flex items-start">
                  <span class="w-2 h-2 bg-blue-500 rounded-full mr-2 mt-2"></span>
                  <span><strong>成交量：</strong>关注成交量是否能有效放大</span>
                </div>
              </div>
            </div>

            <!-- 市场预期 -->
            <div class="p-4 bg-purple-50 rounded-lg">
              <div class="flex items-center mb-3">
                <i class="ri-eye-line text-purple-600 mr-2"></i>
                <span class="font-medium text-gray-900">市场预期</span>
              </div>
              <div class="space-y-2 text-sm">
                <div class="flex items-start">
                  <span class="w-2 h-2 bg-green-500 rounded-full mr-2 mt-2"></span>
                  <span>春节备货需求预期推动白酒板块情绪回暖</span>
                </div>
                <div class="flex items-start">
                  <span class="w-2 h-2 bg-blue-500 rounded-full mr-2 mt-2"></span>
                  <span>机构调研频繁，市场关注度持续提升</span>
                </div>
                <div class="flex items-start">
                  <span class="w-2 h-2 bg-yellow-500 rounded-full mr-2 mt-2"></span>
                  <span>需关注宏观经济数据对消费板块的影响</span>
                </div>
              </div>
            </div>

            <!-- 风险提示 -->
            <div class="p-4 bg-red-50 rounded-lg border border-red-200">
              <div class="flex items-center mb-2">
                <i class="ri-alert-line text-red-600 mr-2"></i>
                <span class="font-medium text-gray-900">风险提示</span>
              </div>
              <div class="text-sm text-red-700">
                当前估值偏高，短期存在回调风险。建议控制仓位，分批建仓，严格执行止损策略。
              </div>
            </div>
          </div>
        </div>

        <!-- 盘后总结内容 -->
        <div id="evening-report" class="tab-content p-4 hidden">
          <div class="space-y-4">
            <!-- 报告时间 -->
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center text-sm text-gray-600">
                <i class="ri-time-line mr-1"></i>
                <span>2024年1月15日 15:30 盘后总结</span>
              </div>
            </div>

            <!-- 今日表现总结 -->
            <div class="p-4 bg-green-50 rounded-lg border border-green-200">
              <div class="flex items-center mb-3">
                <i class="ri-trophy-line text-green-600 mr-2"></i>
                <span class="font-medium text-gray-900">今日表现总结</span>
              </div>
              <div class="bg-white rounded-lg p-3">
                <div class="grid grid-cols-2 gap-4 mb-3">
                  <div>
                    <div class="text-xs text-gray-600 mb-1">今日涨跌</div>
                    <div class="text-lg font-bold text-red-500">+2.35%</div>
                  </div>
                  <div>
                    <div class="text-xs text-gray-600 mb-1">相对大盘</div>
                    <div class="text-lg font-bold text-green-600">+1.8%</div>
                  </div>
                </div>
                <p class="text-sm text-gray-700">
                  今日茅台表现强势，成功突破前期阻力位，成交量明显放大，资金关注度提升。
                </p>
              </div>
            </div>

            <!-- 技术面分析 -->
            <div class="p-4 bg-purple-50 rounded-lg">
              <div class="flex items-center mb-3">
                <i class="ri-line-chart-line text-purple-600 mr-2"></i>
                <span class="font-medium text-gray-900">技术面分析</span>
              </div>
              <div class="space-y-2 text-sm">
                <div class="flex items-start">
                  <span class="w-2 h-2 bg-green-500 rounded-full mr-2 mt-2"></span>
                  <span><strong>突破确认：</strong>成功突破¥1,680阻力位，多头趋势确立</span>
                </div>
                <div class="flex items-start">
                  <span class="w-2 h-2 bg-blue-500 rounded-full mr-2 mt-2"></span>
                  <span><strong>成交量：</strong>放量上涨，资金流入明显</span>
                </div>
                <div class="flex items-start">
                  <span class="w-2 h-2 bg-green-500 rounded-full mr-2 mt-2"></span>
                  <span><strong>技术指标：</strong>MACD金叉向上，RSI进入强势区间</span>
                </div>
              </div>
            </div>

            <!-- 基本面分析 -->
            <div class="p-4 bg-blue-50 rounded-lg">
              <div class="flex items-center mb-3">
                <i class="ri-building-line text-blue-600 mr-2"></i>
                <span class="font-medium text-gray-900">基本面分析</span>
              </div>
              <div class="space-y-2 text-sm">
                <div class="flex items-start">
                  <span class="w-2 h-2 bg-green-500 rounded-full mr-2 mt-2"></span>
                  <span>三季度业绩超预期，ROE达到25.8%</span>
                </div>
                <div class="flex items-start">
                  <span class="w-2 h-2 bg-blue-500 rounded-full mr-2 mt-2"></span>
                  <span>白酒行业景气度回升，春节备货需求旺盛</span>
                </div>
                <div class="flex items-start">
                  <span class="w-2 h-2 bg-green-500 rounded-full mr-2 mt-2"></span>
                  <span>品牌护城河深厚，定价权强</span>
                </div>
              </div>
            </div>

            <!-- 市场情绪 -->
            <div class="p-4 bg-yellow-50 rounded-lg">
              <div class="flex items-center mb-3">
                <i class="ri-emotion-line text-yellow-600 mr-2"></i>
                <span class="font-medium text-gray-900">市场情绪</span>
              </div>
              <div class="space-y-2 text-sm">
                <div class="flex items-start">
                  <span class="w-2 h-2 bg-green-500 rounded-full mr-2 mt-2"></span>
                  <span>机构资金持续流入，北向资金净买入</span>
                </div>
                <div class="flex items-start">
                  <span class="w-2 h-2 bg-blue-500 rounded-full mr-2 mt-2"></span>
                  <span>白酒板块整体表现强势</span>
                </div>
                <div class="flex items-start">
                  <span class="w-2 h-2 bg-green-500 rounded-full mr-2 mt-2"></span>
                  <span>市场对春节行情预期乐观</span>
                </div>
              </div>
            </div>

            <!-- 明日展望 -->
            <div class="p-4 bg-indigo-50 rounded-lg">
              <div class="flex items-center mb-3">
                <i class="ri-calendar-line text-indigo-600 mr-2"></i>
                <span class="font-medium text-gray-900">明日展望</span>
              </div>
              <div class="space-y-2 text-sm">
                <div class="flex items-start">
                  <span class="w-2 h-2 bg-green-500 rounded-full mr-2 mt-2"></span>
                  <span><strong>支撑位：</strong>¥1,650（今日突破位转为支撑）</span>
                </div>
                <div class="flex items-start">
                  <span class="w-2 h-2 bg-red-500 rounded-full mr-2 mt-2"></span>
                  <span><strong>阻力位：</strong>¥1,720（前期高点）</span>
                </div>
                <div class="flex items-start">
                  <span class="w-2 h-2 bg-blue-500 rounded-full mr-2 mt-2"></span>
                  <span><strong>操作建议：</strong>回调至支撑位可考虑加仓</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 操作按钮 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="grid ">
          <button onclick="addToWatchlist()" class="flex items-center justify-center space-x-4 py-6 border border-primary text-primary rounded-lg hover:bg-blue-50">
            <i class="ri-star-line"></i>
            <span>加入自选</span>
          </button>
        </div>
      </div>
    </section>
  </main>

  <!-- 底部导航栏 -->
  <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
    <div class="grid grid-cols-4 py-2">
      <a href="index.html" class="flex flex-col items-center py-2 text-gray-600">
        <i class="ri-home-line text-xl mb-1"></i>
        <span class="text-xs">首页</span>
      </a>
      <a href="watchlist.html" class="flex flex-col items-center py-2 text-gray-600">
        <i class="ri-star-line text-xl mb-1"></i>
        <span class="text-xs">自选股</span>
      </a>
      <a href="reports.html" class="flex flex-col items-center py-2 text-gray-600">
        <i class="ri-file-text-line text-xl mb-1"></i>
        <span class="text-xs">报告中心</span>
      </a>
      <a href="关于我们.html" class="flex flex-col items-center py-2 text-gray-600">
        <i class="ri-user-line text-xl mb-1"></i>
        <span class="text-xs">我的</span>
      </a>
    </div>
  </nav>

  <script>
    // 股票数据库
    const stockDatabase = {
      '600519': { 
        name: '贵州茅台', 
        price: 1678.50, 
        change: '+2.35%', 
        changeAmount: '+38.50',
        open: 1645.00,
        high: 1685.20,
        low: 1638.80,
        volume: '1.2万手'
      },
      '002594': { 
        name: '比亚迪', 
        price: 245.80, 
        change: '+3.25%', 
        changeAmount: '+7.75',
        open: 238.50,
        high: 248.90,
        low: 236.20,
        volume: '8.5万手'
      },
      '300750': { 
        name: '宁德时代', 
        price: 185.20, 
        change: '-1.85%', 
        changeAmount: '-3.50',
        open: 188.00,
        high: 189.50,
        low: 184.10,
        volume: '12.3万手'
      },
      '601318': { 
        name: '中国平安', 
        price: 42.50, 
        change: '-0.93%', 
        changeAmount: '-0.40',
        open: 42.80,
        high: 43.20,
        low: 42.30,
        volume: '15.6万手'
      }
    };

    let currentStock = null;

    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 从URL参数获取股票信息
      const urlParams = new URLSearchParams(window.location.search);
      const stockCode = urlParams.get('code') || urlParams.get('stock') || '600519';
      
      // 更新股票信息
      updateStockInfo(stockCode);
      
      // AI引擎选择
      initAIEngineSelection();
    });

    // 更新股票信息
    function updateStockInfo(stockCode) {
      const stock = stockDatabase[stockCode];
      if (!stock) return;
      
      currentStock = { code: stockCode, ...stock };
      
      document.getElementById('stockName').textContent = stock.name;
      document.getElementById('stockCode').textContent = stockCode;
      document.getElementById('stockPrice').textContent = `¥${stock.price}`;
      
      const changeElement = document.getElementById('stockChange');
      changeElement.textContent = `${stock.change} (${stock.changeAmount})`;
      changeElement.className = stock.change.startsWith('+') ? 'text-sm text-red-500' : 'text-sm text-green-500';
      
      // 更新其他数据
      const dataElements = document.querySelectorAll('.grid div');
      if (dataElements.length >= 4) {
        dataElements[0].querySelector('.text-sm').textContent = `¥${stock.open}`;
        dataElements[1].querySelector('.text-sm').textContent = `¥${stock.high}`;
        dataElements[2].querySelector('.text-sm').textContent = `¥${stock.low}`;
        dataElements[3].querySelector('.text-sm').textContent = stock.volume;
      }
    }

    // 标签页切换
    function switchTab(tabName) {
      // 更新标签按钮状态
      document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active', 'text-primary', 'border-primary');
        btn.classList.add('text-gray-500');
      });
      
      // 隐藏所有内容
      document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
      });
      
      // 显示选中的内容
      if (tabName === 'morning') {
        document.querySelector('[onclick="switchTab(\'morning\')"]').classList.add('active', 'text-primary', 'border-primary');
        document.querySelector('[onclick="switchTab(\'morning\')"]').classList.remove('text-gray-500');
        document.getElementById('morning-report').classList.remove('hidden');
      } else {
        document.querySelector('[onclick="switchTab(\'evening\')"]').classList.add('active', 'text-primary', 'border-primary');
        document.querySelector('[onclick="switchTab(\'evening\')"]').classList.remove('text-gray-500');
        document.getElementById('evening-report').classList.remove('hidden');
      }
    }

    // AI引擎选择
    function initAIEngineSelection() {
      document.querySelectorAll('.ai-engine').forEach(engine => {
        engine.addEventListener('click', function() {
          // 移除所有active状态
          document.querySelectorAll('.ai-engine').forEach(e => {
            e.classList.remove('active', 'border-primary', 'bg-blue-50');
            e.classList.add('border-gray-200', 'bg-white');
          });
          
          // 添加active状态到当前选中的引擎
          this.classList.add('active', 'border-primary', 'bg-blue-50');
          this.classList.remove('border-gray-200', 'bg-white');
        });
      });
    }

    // 加入自选股
    function addToWatchlist() {
      if (!currentStock) return;
      alert(`${currentStock.name} 已添加到自选股`);
    }

    // 打开模拟交易
    function openSimulation() {
      window.location.href = '模拟盘.html';
    }

    // 切换报告类型
    function switchReportType() {
      const reportType = document.getElementById('reportTypeSelector').value;
      const urlParams = new URLSearchParams(window.location.search);
      const stockCode = urlParams.get('code') || currentStock?.code || '600519';
      const stockName = currentStock?.name || '贵州茅台';

      if (reportType === 'weekly') {
        // 跳转到详细报告页面的周报模式
        const reportUrl = `详细报告.html?code=${stockCode}&name=${encodeURIComponent(stockName)}&type=weekly&date=${new Date().toISOString().split('T')[0]}`;
        window.location.href = reportUrl;
      } else {
        // 保持在日报页面
        document.getElementById('report-description').textContent = '查看该股票的详细技术分析和短期操作建议';
      }
    }

    // 分享报告
    function shareReport() {
      if (!currentStock) return;
      alert(`${currentStock.name} 日报已复制到剪贴板`);
    }

    // 返回功能
    function goBack() {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        window.location.href = 'index.html';
      }
    }
  </script>
</body>
</html>
