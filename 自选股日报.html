<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>自选股日报 - 金融智能体</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
  <style>
    .gradient-bg {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .report-header {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    .stock-card {
      transition: all 0.3s ease;
    }
    .stock-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    .trend-up { color: #ef4444; }
    .trend-down { color: #22c55e; }
    .market-summary {
      background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    }
  </style>
</head>
<body class="bg-gray-50">
  <!-- 顶部导航栏 -->
  <header class="bg-white shadow-sm fixed top-0 left-0 right-0 z-50">
    <div class="flex items-center justify-between px-4 py-3">
      <div class="flex items-center space-x-3">
        <button onclick="goBack()" class="p-2 hover:bg-gray-100 rounded-lg">
          <i class="ri-arrow-left-line text-xl text-gray-600"></i>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">自选股日报</h1>
      </div>
      <div class="flex items-center space-x-2">
        <button onclick="shareReport()" class="p-2 hover:bg-gray-100 rounded-lg">
          <i class="ri-share-line text-xl text-gray-600"></i>
        </button>
        <button onclick="refreshReport()" class="p-2 hover:bg-gray-100 rounded-lg">
          <i class="ri-refresh-line text-xl text-gray-600"></i>
        </button>
      </div>
    </div>
  </header>

  <!-- 主要内容区域 -->
  <main class="pt-16 pb-20 px-4">
    <!-- 报告头部 -->
    <section class="mb-6">
      <div class="report-header rounded-xl p-6 text-white">
        <div class="flex items-center justify-between mb-4">
          <div>
            <h2 class="text-2xl font-bold mb-1">自选股日报</h2>
            <p class="text-sm opacity-90" id="report-date">2024年1月15日 星期一</p>
          </div>
          <div class="text-right">
            <div class="text-sm opacity-90">推送时间</div>
            <div class="text-lg font-semibold" id="push-time">09:00</div>
          </div>
        </div>
        <div class="bg-white bg-opacity-20 rounded-lg p-3">
          <div class="text-sm opacity-90 mb-1">今日关注</div>
          <div class="text-lg font-semibold" id="focus-count">4只自选股</div>
        </div>
      </div>
    </section>

    <!-- 市场概况 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <h3 class="font-semibold text-gray-900 mb-4 flex items-center">
          <i class="ri-line-chart-line text-primary mr-2"></i>
          市场概况
        </h3>
        <div class="market-summary rounded-lg p-4 mb-4">
          <div class="grid grid-cols-3 gap-4 text-center">
            <div>
              <div class="text-lg font-bold text-gray-900">3156.82</div>
              <div class="text-xs text-gray-600">上证指数</div>
              <div class="text-sm text-red-500">+0.85%</div>
            </div>
            <div>
              <div class="text-lg font-bold text-gray-900">1952.65</div>
              <div class="text-xs text-gray-600">深证成指</div>
              <div class="text-sm text-green-500">-0.32%</div>
            </div>
            <div>
              <div class="text-lg font-bold text-gray-900">2156.43</div>
              <div class="text-xs text-gray-600">创业板指</div>
              <div class="text-sm text-red-500">+1.24%</div>
            </div>
          </div>
        </div>
        <div class="text-sm text-gray-700 leading-relaxed">
          <p class="mb-2">
            <span class="font-medium">市场表现：</span>
            今日A股三大指数涨跌不一，上证指数小幅上涨0.85%，创业板指表现较强上涨1.24%。
            成交量较昨日放大15%，市场情绪有所回暖。
          </p>
          <p>
            <span class="font-medium">热点板块：</span>
            新能源汽车、人工智能、生物医药板块领涨，传统金融、地产板块表现疲软。
          </p>
        </div>
      </div>
    </section>

    <!-- 自选股分析 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <h3 class="font-semibold text-gray-900 mb-4 flex items-center">
          <i class="ri-star-line text-yellow-500 mr-2"></i>
          自选股表现
        </h3>
        <div class="space-y-4" id="watchlist-analysis">
          <!-- 股票分析卡片将通过JavaScript动态生成 -->
        </div>
      </div>
    </section>

    <!-- AI投资建议 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <h3 class="font-semibold text-gray-900 mb-4 flex items-center">
          <i class="ri-robot-line text-purple-500 mr-2"></i>
          AI投资建议
        </h3>
        <div class="space-y-4">
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 class="font-medium text-blue-800 mb-2">
              <i class="ri-lightbulb-line mr-1"></i>今日操作建议
            </h4>
            <div class="text-sm text-blue-700 space-y-2">
              <p>• <strong>贵州茅台</strong>：建议继续持有，白酒板块估值修复行情延续</p>
              <p>• <strong>比亚迪</strong>：可考虑适当加仓，新能源汽车销量数据亮眼</p>
              <p>• <strong>宁德时代</strong>：建议观望，等待更好的入场时机</p>
              <p>• <strong>中国平安</strong>：建议减仓，金融板块短期承压</p>
            </div>
          </div>
          
          <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 class="font-medium text-yellow-800 mb-2">
              <i class="ri-alert-line mr-1"></i>风险提示
            </h4>
            <div class="text-sm text-yellow-700">
              <p>当前市场波动较大，建议控制仓位在70%以下，注意分散投资风险。
              关注美联储政策变化和国内宏观经济数据对市场的影响。</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 明日关注 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <h3 class="font-semibold text-gray-900 mb-4 flex items-center">
          <i class="ri-calendar-todo-line text-green-500 mr-2"></i>
          明日关注
        </h3>
        <div class="space-y-3">
          <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
            <i class="ri-time-line text-gray-500 mt-0.5"></i>
            <div>
              <div class="font-medium text-gray-900">09:30 开盘关注</div>
              <div class="text-sm text-gray-600">关注新能源汽车板块开盘表现，重点关注比亚迪是否能突破250元关键位</div>
            </div>
          </div>
          <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
            <i class="ri-file-text-line text-gray-500 mt-0.5"></i>
            <div>
              <div class="font-medium text-gray-900">重要公告</div>
              <div class="text-sm text-gray-600">贵州茅台将于明日发布年度业绩预告，预计业绩增长15-20%</div>
            </div>
          </div>
          <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
            <i class="ri-global-line text-gray-500 mt-0.5"></i>
            <div>
              <div class="font-medium text-gray-900">海外市场</div>
              <div class="text-sm text-gray-600">关注美股科技股表现，特斯拉财报可能影响新能源汽车板块情绪</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 报告说明 -->
    <section class="mb-6">
      <div class="bg-gray-50 rounded-xl p-4">
        <div class="flex items-center justify-between text-xs text-gray-500 mb-2">
          <div class="flex items-center">
            <i class="ri-robot-line mr-1"></i>
            <span>AI智投分析引擎生成</span>
          </div>
          <div id="generate-time">生成时间：2024-01-15 08:45</div>
        </div>
        <div class="text-xs text-gray-400 leading-relaxed">
          本报告基于您的自选股和公开市场信息，通过AI算法分析生成，仅供参考，不构成投资建议。
          投资有风险，入市需谨慎。建议结合个人风险承受能力和投资目标进行决策。
        </div>
      </div>
    </section>
  </main>

  <!-- 底部导航栏 -->
  <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
    <div class="grid grid-cols-4 py-2">
      <a href="index.html" class="flex flex-col items-center py-2 text-gray-600">
        <i class="ri-home-line text-xl mb-1"></i>
        <span class="text-xs">首页</span>
      </a>
      <a href="watchlist.html" class="flex flex-col items-center py-2 text-gray-600">
        <i class="ri-star-line text-xl mb-1"></i>
        <span class="text-xs">自选股</span>
      </a>
      <a href="reports.html" class="flex flex-col items-center py-2 text-primary">
        <i class="ri-file-text-line text-xl mb-1"></i>
        <span class="text-xs">报告中心</span>
      </a>
      <a href="关于我们.html" class="flex flex-col items-center py-2 text-gray-600">
        <i class="ri-user-line text-xl mb-1"></i>
        <span class="text-xs">我的</span>
      </a>
    </div>
  </nav>

  <script>
    // 自选股数据
    const watchlistData = [
      {
        name: '贵州茅台',
        code: '600519',
        price: '1678.50',
        change: '+2.35%',
        changeAmount: '+38.50',
        volume: '1.2亿',
        analysis: '白酒龙头地位稳固，春节备货需求旺盛，股价突破前期高点，技术面呈现多头排列。',
        suggestion: '持有',
        reason: '基本面扎实，估值合理，建议长期持有',
        trend: 'up'
      },
      {
        name: '比亚迪',
        code: '002594',
        price: '245.80',
        change: '+3.25%',
        changeAmount: '+7.75',
        volume: '8.5亿',
        analysis: '新能源汽车销量持续增长，12月销量突破30万辆创历史新高，产业链优势明显。',
        suggestion: '买入',
        reason: '行业景气度高，公司竞争优势突出',
        trend: 'up'
      },
      {
        name: '宁德时代',
        code: '300750',
        price: '185.20',
        change: '-1.85%',
        changeAmount: '-3.50',
        volume: '12.3亿',
        analysis: '电池技术领先，但短期面临原材料成本压力，股价在重要支撑位附近震荡。',
        suggestion: '观望',
        reason: '等待成本压力缓解，寻找更好入场时机',
        trend: 'down'
      },
      {
        name: '中国平安',
        code: '601318',
        price: '42.50',
        change: '-0.93%',
        changeAmount: '-0.40',
        volume: '15.6亿',
        analysis: '保险业务稳健增长，但受利率环境影响，投资收益承压，估值处于历史低位。',
        suggestion: '减仓',
        reason: '短期基本面承压，建议适当减仓',
        trend: 'down'
      }
    ];

    // 生成自选股分析内容
    function generateWatchlistAnalysis() {
      const container = document.getElementById('watchlist-analysis');
      let html = '';

      watchlistData.forEach(stock => {
        const trendClass = stock.trend === 'up' ? 'trend-up' : 'trend-down';
        const suggestionColor = getSuggestionColor(stock.suggestion);
        
        html += `
          <div class="stock-card border border-gray-200 rounded-lg p-4">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center space-x-3">
                <div>
                  <h4 class="font-semibold text-gray-900">${stock.name}</h4>
                  <div class="text-xs text-gray-500">${stock.code}</div>
                </div>
              </div>
              <div class="text-right">
                <div class="text-lg font-bold text-gray-900">¥${stock.price}</div>
                <div class="text-sm ${trendClass}">${stock.change}</div>
              </div>
            </div>
            
            <div class="grid grid-cols-2 gap-4 mb-3 text-sm">
              <div>
                <span class="text-gray-500">涨跌额：</span>
                <span class="${trendClass}">${stock.changeAmount}</span>
              </div>
              <div>
                <span class="text-gray-500">成交额：</span>
                <span class="text-gray-900">${stock.volume}</span>
              </div>
            </div>
            
            <div class="mb-3">
              <div class="text-sm text-gray-700 leading-relaxed">${stock.analysis}</div>
            </div>
            
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <span class="text-xs ${suggestionColor} px-2 py-1 rounded">${stock.suggestion}</span>
                <span class="text-xs text-gray-500">${stock.reason}</span>
              </div>
              <button onclick="viewStockDetail('${stock.code}', '${stock.name}')" 
                      class="text-xs text-primary hover:text-blue-700">
                查看详情 <i class="ri-arrow-right-s-line"></i>
              </button>
            </div>
          </div>
        `;
      });

      container.innerHTML = html;
    }

    // 获取建议颜色
    function getSuggestionColor(suggestion) {
      switch(suggestion) {
        case '买入': return 'bg-green-100 text-green-700';
        case '持有': return 'bg-blue-100 text-blue-700';
        case '观望': return 'bg-yellow-100 text-yellow-700';
        case '减仓': return 'bg-red-100 text-red-700';
        default: return 'bg-gray-100 text-gray-700';
      }
    }

    // 查看股票详情
    function viewStockDetail(code, name) {
      window.location.href = `股票分析.html?code=${code}&name=${encodeURIComponent(name)}`;
    }

    // 返回功能
    function goBack() {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        window.location.href = 'reports.html';
      }
    }

    // 分享报告
    function shareReport() {
      if (navigator.share) {
        navigator.share({
          title: '自选股日报 - 金融智能体',
          text: '查看我的自选股今日表现和AI投资建议',
          url: window.location.href
        }).catch(err => {
          console.log('分享失败:', err);
        });
      } else {
        if (navigator.clipboard) {
          navigator.clipboard.writeText(window.location.href).then(() => {
            alert('链接已复制到剪贴板');
          });
        }
      }
    }

    // 刷新报告
    function refreshReport() {
      const refreshBtn = document.querySelector('[onclick="refreshReport()"] i');
      refreshBtn.classList.add('animate-spin');
      
      setTimeout(() => {
        refreshBtn.classList.remove('animate-spin');
        generateWatchlistAnalysis();
        alert('报告已更新');
      }, 1500);
    }

    // 初始化页面
    document.addEventListener('DOMContentLoaded', function() {
      // 设置当前日期和时间
      const now = new Date();
      const dateStr = now.toLocaleDateString('zh-CN', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        weekday: 'long'
      });
      const timeStr = now.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
      
      document.getElementById('report-date').textContent = dateStr;
      document.getElementById('push-time').textContent = timeStr;
      document.getElementById('generate-time').textContent = `生成时间：${now.toLocaleString('zh-CN')}`;
      document.getElementById('focus-count').textContent = `${watchlistData.length}只自选股`;
      
      // 生成自选股分析
      generateWatchlistAnalysis();
    });
  </script>
</body>
</html>
