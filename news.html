<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>最新资讯 - 金融智能体</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: "#3B82F6",
            secondary: "#F1F5F9",
          },
        },
      },
    };
  </script>
</head>
<body class="bg-gray-50 min-h-screen">
  <!-- 顶部导航栏 -->
  <nav class="fixed top-0 w-full bg-white z-50 px-4 py-3 border-b border-gray-100">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <button onclick="goBack()" class="w-8 h-8 flex items-center justify-center mr-3 cursor-pointer">
          <i class="ri-arrow-left-line text-gray-600 text-lg"></i>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">最新资讯</h1>
      </div>
      <div class="flex items-center space-x-2">
        <!-- 搜索按钮 -->
        <button onclick="openSearch()" class="w-8 h-8 flex items-center justify-center">
          <i class="ri-search-line text-gray-600 text-lg"></i>
        </button>
        <!-- 消息中心按钮 -->
        <button onclick="openMessageCenter()" class="w-8 h-8 flex items-center justify-center relative">
          <i class="ri-notification-line text-gray-600 text-lg"></i>
          <!-- 未读消息提示点 -->
          <div class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
        </button>
        <!-- 微信小程序分享按钮 -->
        <button onclick="shareApp()" class="w-8 h-8 flex items-center justify-center">
          <div class="flex space-x-0.5">
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
          </div>
        </button>
        <!-- 微信小程序返回主界面按钮 -->
        <button class="w-8 h-8 flex items-center justify-center">
          <div class="w-5 h-5 border-2 border-gray-600 rounded-full flex items-center justify-center">
            <div class="w-2 h-2 bg-gray-600 rounded-full"></div>
          </div>
        </button>
      </div>
    </div>
  </nav>

  <!-- 主要内容区域 -->
  <main class="pt-16 pb-20 px-4">
    <!-- 主要选项卡：要闻和自选 -->
    <section class="mb-4">
      <div class="bg-white rounded-xl shadow-sm">
        <div class="flex">
          <button onclick="switchMainTab('news')" class="main-tab active flex-1 py-4 text-center font-semibold text-lg border-b-2 border-primary text-primary">
            要闻
          </button>
          <button onclick="switchMainTab('watchlist')" class="main-tab flex-1 py-4 text-center font-semibold text-lg border-b-2 border-transparent text-gray-500">
            自选
          </button>
        </div>
      </div>
    </section>

    <!-- 筛选区域 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="flex items-center justify-between mb-3">
          <div class="flex space-x-2 overflow-x-auto flex-1">
            <button onclick="filterNews('all')" class="news-filter active px-3 py-1.5 rounded-full text-sm font-medium whitespace-nowrap bg-primary text-white">
              全部
            </button>
            <button onclick="filterNews('important')" class="news-filter px-3 py-1.5 rounded-full text-sm font-medium whitespace-nowrap bg-gray-100 text-gray-600">
              重要
            </button>
            <button onclick="filterNews('policy')" class="news-filter px-3 py-1.5 rounded-full text-sm font-medium whitespace-nowrap bg-gray-100 text-gray-600">
              政策
            </button>

          </div>

          <!-- 股票筛选下拉菜单 -->
          <div class="relative ml-3">
            <button onclick="toggleStockFilter()" class="flex items-center space-x-1 px-3 py-1.5 bg-gray-100 rounded-full text-sm font-medium">
              <span id="selected-stock-filter">全市场</span>
              <i class="ri-arrow-down-s-line text-xs"></i>
            </button>
            <!-- 下拉菜单 -->
            <div id="stock-filter-dropdown" class="hidden absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-20 min-w-[120px]">
              <div class="py-1">
                <button onclick="selectStockFilter('all', '全市场')" class="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 flex items-center justify-between">
                  <span>全市场</span>
                  <i class="ri-check-line text-primary opacity-0" id="check-all"></i>
                </button>
                <button onclick="selectStockFilter('600519', '贵州茅台')" class="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 flex items-center justify-between">
                  <span>贵州茅台</span>
                  <i class="ri-check-line text-primary opacity-0" id="check-600519"></i>
                </button>
                <button onclick="selectStockFilter('002594', '比亚迪')" class="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 flex items-center justify-between">
                  <span>比亚迪</span>
                  <i class="ri-check-line text-primary opacity-0" id="check-002594"></i>
                </button>
                <button onclick="selectStockFilter('300750', '宁德时代')" class="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 flex items-center justify-between">
                  <span>宁德时代</span>
                  <i class="ri-check-line text-primary opacity-0" id="check-300750"></i>
                </button>
                <button onclick="selectStockFilter('000858', '五粮液')" class="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 flex items-center justify-between">
                  <span>五粮液</span>
                  <i class="ri-check-line text-primary opacity-0" id="check-000858"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 要闻内容 -->
    <div id="news-content" class="tab-content">
      <div class="space-y-3" id="news-list">
        <!-- 重要资讯 -->
        <div class="news-item bg-white rounded-lg p-4 shadow-sm border-l-4 border-red-500" data-category="important" data-stock="all" onclick="openNews('news1')">
          <div class="mb-2">
            <h3 class="font-medium text-gray-900 mb-1 leading-tight">央行宣布降准0.5个百分点，释放长期资金约1万亿元</h3>
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <span class="text-xs text-gray-500">07-26 18:36</span>
                <span class="text-xs bg-red-100 text-red-700 px-2 py-0.5 rounded">重要</span>
              </div>
              <div class="flex items-center space-x-1">
                <i class="ri-eye-line text-gray-400 text-xs"></i>
                <span class="text-xs text-gray-500">1.2万</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 市场动态 -->
        <div class="news-item bg-white rounded-lg p-4 shadow-sm border-l-4 border-green-500" data-category="market" data-stock="all" onclick="openNews('news2')">
          <div class="mb-2">
            <h3 class="font-medium text-gray-900 mb-1 leading-tight">A股三大指数集体上涨，创业板指涨超2%，银行股领涨</h3>
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <span class="text-xs text-gray-500">07-26 15:30</span>
                <span class="text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded">市场</span>
              </div>
              <div class="flex items-center space-x-1">
                <i class="ri-eye-line text-gray-400 text-xs"></i>
                <span class="text-xs text-gray-500">8.5千</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 贵州茅台相关 -->
        <div class="news-item bg-white rounded-lg p-4 shadow-sm border-l-4 border-purple-500" data-category="important" data-stock="600519" onclick="openNews('news3')">
          <div class="mb-2">
            <h3 class="font-medium text-gray-900 mb-1 leading-tight">贵州茅台三季度业绩超预期，营收同比增长15%</h3>
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <span class="text-xs text-gray-500">07-26 14:20</span>
                <span class="text-xs text-purple-600 font-medium">贵州茅台</span>
                <span class="text-red-600 text-xs">+2.35%</span>
                <span class="text-xs text-gray-500">涨后</span>
              </div>
              <div class="flex items-center space-x-1">
                <i class="ri-eye-line text-gray-400 text-xs"></i>
                <span class="text-xs text-gray-500">6.8千</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 比亚迪相关 -->
        <div class="news-item bg-white rounded-lg p-4 shadow-sm border-l-4 border-blue-500" data-category="market" data-stock="002594" onclick="openNews('news4')">
          <div class="mb-2">
            <h3 class="font-medium text-gray-900 mb-1 leading-tight">比亚迪10月新能源汽车销量突破30万辆创新高</h3>
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <span class="text-xs text-gray-500">07-26 13:15</span>
                <span class="text-xs text-blue-600 font-medium">比亚迪</span>
                <span class="text-red-600 text-xs">+3.25%</span>
                <span class="text-xs text-gray-500">涨后</span>
              </div>
              <div class="flex items-center space-x-1">
                <i class="ri-eye-line text-gray-400 text-xs"></i>
                <span class="text-xs text-gray-500">5.2千</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 宁德时代相关 -->
        <div class="news-item bg-white rounded-lg p-4 shadow-sm border-l-4 border-orange-500" data-category="important" data-stock="300750" onclick="openNews('news5')">
          <div class="mb-2">
            <h3 class="font-medium text-gray-900 mb-1 leading-tight">宁德时代与多家车企签署长期供货协议</h3>
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <span class="text-xs text-gray-500">07-26 11:45</span>
                <span class="text-xs text-orange-600 font-medium">宁德时代</span>
                <span class="text-green-600 text-xs">-0.65%</span>
                <span class="text-xs text-gray-500">跌后</span>
              </div>
              <div class="flex items-center space-x-1">
                <i class="ri-eye-line text-gray-400 text-xs"></i>
                <span class="text-xs text-gray-500">4.1千</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 政策解读 -->
        <div class="news-item bg-white rounded-lg p-4 shadow-sm border-l-4 border-yellow-500" data-category="policy" data-stock="all" onclick="openNews('news6')">
          <div class="mb-2">
            <h3 class="font-medium text-gray-900 mb-1 leading-tight">证监会发布新规，进一步规范上市公司信息披露</h3>
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <span class="text-xs text-gray-500">07-26 10:30</span>
                <span class="text-xs bg-yellow-100 text-yellow-700 px-2 py-0.5 rounded">政策</span>
              </div>
              <div class="flex items-center space-x-1">
                <i class="ri-eye-line text-gray-400 text-xs"></i>
                <span class="text-xs text-gray-500">4.2千</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 五粮液相关 -->
        <div class="news-item bg-white rounded-lg p-4 shadow-sm border-l-4 border-indigo-500" data-category="market" data-stock="000858" onclick="openNews('news7')">
          <div class="mb-2">
            <h3 class="font-medium text-gray-900 mb-1 leading-tight">五粮液推出高端新品，进军超高端白酒市场</h3>
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <span class="text-xs text-gray-500">07-26 09:15</span>
                <span class="text-xs text-indigo-600 font-medium">五粮液</span>
                <span class="text-red-600 text-xs">+1.85%</span>
                <span class="text-xs text-gray-500">涨后</span>
              </div>
              <div class="flex items-center space-x-1">
                <i class="ri-eye-line text-gray-400 text-xs"></i>
                <span class="text-xs text-gray-500">3.8千</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 自选股内容 -->
    <div id="watchlist-content" class="tab-content hidden">
      <div class="space-y-3" id="watchlist-news-list">
        <!-- 自选股相关新闻将在这里动态加载 -->
        <div class="text-center py-12">
          <div class="mb-4">
            <i class="ri-star-line text-4xl text-gray-300"></i>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">选择自选股查看相关资讯</h3>
          <p class="text-gray-500 text-sm mb-4">从上方下拉菜单选择您关注的股票</p>
          <button onclick="selectStockFilter('600519', '贵州茅台')" class="px-4 py-2 bg-primary text-white rounded-lg text-sm">
            查看贵州茅台资讯
          </button>
        </div>
      </div>
    </div>


  </main>

  <script>
    let currentFilter = 'all';
    let currentMainTab = 'news';
    let currentStockFilter = 'all';

    function goBack() {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        window.location.href = 'index.html';
      }
    }

    function openSearch() {
      window.location.href = 'search.html';
    }

    function openMessageCenter() {
      window.location.href = '消息中心.html';
    }

    function openAIChat() {
      window.location.href = 'AI选股.html#ai-chat';
    }

    function shareApp() {
      if (navigator.share) {
        navigator.share({
          title: '金融智能体 - 最新资讯',
          text: '获取最新金融资讯和市场动态',
          url: window.location.href
        }).catch(err => {
          console.log('分享失败:', err);
        });
      } else {
        if (navigator.clipboard) {
          navigator.clipboard.writeText(window.location.href).then(() => {
            alert('链接已复制到剪贴板');
          });
        }
      }
    }

    // 切换主要选项卡（要闻/自选）
    function switchMainTab(tab) {
      currentMainTab = tab;

      // 更新选项卡状态
      document.querySelectorAll('.main-tab').forEach(btn => {
        btn.classList.remove('active', 'border-primary', 'text-primary');
        btn.classList.add('border-transparent', 'text-gray-500');
      });

      event.target.classList.add('active', 'border-primary', 'text-primary');
      event.target.classList.remove('border-transparent', 'text-gray-500');

      // 显示对应内容
      document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
      });

      if (tab === 'news') {
        document.getElementById('news-content').classList.remove('hidden');
      } else {
        document.getElementById('watchlist-content').classList.remove('hidden');
        loadWatchlistNews();
      }
    }

    // 加载自选股相关新闻
    function loadWatchlistNews() {
      const watchlistContainer = document.getElementById('watchlist-news-list');

      if (currentStockFilter === 'all') {
        watchlistContainer.innerHTML = `
          <div class="text-center py-12">
            <div class="mb-4">
              <i class="ri-star-line text-4xl text-gray-300"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">选择自选股查看相关资讯</h3>
            <p class="text-gray-500 text-sm mb-4">从上方下拉菜单选择您关注的股票</p>
            <button onclick="selectStockFilter('600519', '贵州茅台')" class="px-4 py-2 bg-primary text-white rounded-lg text-sm">
              查看贵州茅台资讯
            </button>
          </div>
        `;
      } else {
        // 显示特定股票的新闻
        const stockNews = getStockSpecificNews(currentStockFilter);
        watchlistContainer.innerHTML = stockNews;
      }
    }

    // 获取特定股票的新闻
    function getStockSpecificNews(stockCode) {
      const stockNewsData = {
        '600519': [
          {
            title: '贵州茅台三季度业绩超预期，营收同比增长15%',
            time: '07-26 14:20',
            change: '+2.35%',
            type: '涨后',
            views: '6.8千',
            category: 'important'
          },
          {
            title: '机构调研：茅台渠道库存健康，春节备货积极',
            time: '07-26 11:30',
            change: '+2.35%',
            type: '涨后',
            views: '4.2千',
            category: 'market'
          },
          {
            title: '白酒板块集体走强，茅台领涨带动行业情绪',
            time: '07-26 09:45',
            change: '+2.35%',
            type: '涨后',
            views: '5.1千',
            category: 'market'
          }
        ],
        '002594': [
          {
            title: '比亚迪10月新能源汽车销量突破30万辆创新高',
            time: '07-26 13:15',
            change: '+3.25%',
            type: '涨后',
            views: '5.2千',
            category: 'important'
          },
          {
            title: '海外市场拓展顺利，比亚迪欧洲工厂正式投产',
            time: '07-26 10:20',
            change: '+3.25%',
            type: '涨后',
            views: '3.8千',
            category: 'market'
          }
        ],
        '300750': [
          {
            title: '宁德时代与多家车企签署长期供货协议',
            time: '07-26 11:45',
            change: '-0.65%',
            type: '跌后',
            views: '4.1千',
            category: 'important'
          },
          {
            title: '动力电池技术突破，宁德时代发布新一代产品',
            time: '07-26 08:30',
            change: '-0.65%',
            type: '跌后',
            views: '3.5千',
            category: 'market'
          }
        ],
        '000858': [
          {
            title: '五粮液推出高端新品，进军超高端白酒市场',
            time: '07-26 09:15',
            change: '+1.85%',
            type: '涨后',
            views: '3.8千',
            category: 'important'
          }
        ]
      };

      const stockName = getStockName(stockCode);
      const newsItems = stockNewsData[stockCode] || [];

      if (newsItems.length === 0) {
        return `
          <div class="text-center py-12">
            <div class="mb-4">
              <i class="ri-information-line text-4xl text-gray-300"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">暂无${stockName}相关资讯</h3>
            <p class="text-gray-500 text-sm">请稍后再试或选择其他股票</p>
          </div>
        `;
      }

      return newsItems.map((news, index) => `
        <div class="news-item bg-white rounded-lg p-4 shadow-sm border-l-4 ${getBorderColor(news.category)}" onclick="openNews('stock_${stockCode}_${index}')">
          <div class="mb-2">
            <h3 class="font-medium text-gray-900 mb-1 leading-tight">${news.title}</h3>
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <span class="text-xs text-gray-500">${news.time}</span>
                <span class="text-xs font-medium" style="color: ${getStockColor(stockCode)}">${stockName}</span>
                <span class="text-xs ${news.change.startsWith('+') ? 'text-red-600' : 'text-green-600'}">${news.change}</span>
                <span class="text-xs text-gray-500">${news.type}</span>
              </div>
              <div class="flex items-center space-x-1">
                <i class="ri-eye-line text-gray-400 text-xs"></i>
                <span class="text-xs text-gray-500">${news.views}</span>
              </div>
            </div>
          </div>
        </div>
      `).join('');
    }

    // 获取股票名称
    function getStockName(stockCode) {
      const stockNames = {
        '600519': '贵州茅台',
        '002594': '比亚迪',
        '300750': '宁德时代',
        '000858': '五粮液'
      };
      return stockNames[stockCode] || '未知股票';
    }

    // 获取股票颜色
    function getStockColor(stockCode) {
      const colors = {
        '600519': '#8B5CF6',
        '002594': '#3B82F6',
        '300750': '#F59E0B',
        '000858': '#6366F1'
      };
      return colors[stockCode] || '#6B7280';
    }

    // 获取边框颜色
    function getBorderColor(category) {
      const colors = {
        'important': 'border-red-500',
        'market': 'border-blue-500',
        'policy': 'border-yellow-500'
      };
      return colors[category] || 'border-gray-500';
    }

    // 切换股票筛选下拉菜单
    function toggleStockFilter() {
      const dropdown = document.getElementById('stock-filter-dropdown');
      dropdown.classList.toggle('hidden');
    }

    // 选择股票筛选
    function selectStockFilter(stockCode, stockName) {
      currentStockFilter = stockCode;

      // 更新显示文本
      document.getElementById('selected-stock-filter').textContent = stockName;

      // 更新选中状态
      document.querySelectorAll('#stock-filter-dropdown i').forEach(icon => {
        icon.classList.add('opacity-0');
      });
      document.getElementById(`check-${stockCode}`).classList.remove('opacity-0');

      // 关闭下拉菜单
      document.getElementById('stock-filter-dropdown').classList.add('hidden');

      // 根据当前选项卡过滤内容
      if (currentMainTab === 'news') {
        filterNewsByStock(stockCode);
      } else {
        loadWatchlistNews();
      }
    }

    // 按股票过滤新闻
    function filterNewsByStock(stockCode) {
      const newsItems = document.querySelectorAll('#news-list .news-item');
      newsItems.forEach(item => {
        const itemStock = item.getAttribute('data-stock');
        const itemCategory = item.getAttribute('data-category');

        // 同时考虑股票筛选和分类筛选
        const stockMatch = stockCode === 'all' || itemStock === stockCode || itemStock === 'all';
        const categoryMatch = currentFilter === 'all' || itemCategory === currentFilter;

        if (stockMatch && categoryMatch) {
          item.style.display = 'block';
        } else {
          item.style.display = 'none';
        }
      });
    }

    // 新闻分类筛选
    function filterNews(category) {
      currentFilter = category;

      // 更新按钮状态
      document.querySelectorAll('.news-filter').forEach(btn => {
        btn.classList.remove('active', 'bg-primary', 'text-white');
        btn.classList.add('bg-gray-100', 'text-gray-600');
      });

      event.target.classList.add('active', 'bg-primary', 'text-white');
      event.target.classList.remove('bg-gray-100', 'text-gray-600');

      // 应用筛选
      if (currentMainTab === 'news') {
        filterNewsByStock(currentStockFilter);
      }
    }

    function openNews(newsId) {
      window.location.href = `news-detail.html?id=${newsId}`;
    }

    function loadMore() {
      alert('加载更多功能开发中...');
    }

    // 点击外部关闭下拉菜单
    document.addEventListener('click', function(event) {
      const dropdown = document.getElementById('stock-filter-dropdown');
      const button = event.target.closest('button');

      if (!button || !button.onclick || button.onclick.toString().indexOf('toggleStockFilter') === -1) {
        dropdown.classList.add('hidden');
      }
    });

    // 页面加载时设置默认状态
    document.addEventListener('DOMContentLoaded', function() {
      // 设置默认选中状态
      document.querySelector('.news-filter').classList.add('active', 'bg-primary', 'text-white');
      document.getElementById('check-all').classList.remove('opacity-0');
    });
  </script>
</body>
</html>
