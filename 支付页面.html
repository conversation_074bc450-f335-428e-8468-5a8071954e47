<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>支付 - 金融智能体</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: "#3B82F6",
            secondary: "#F1F5F9",
          },
        },
      },
    };
  </script>
  <style>
    .payment-method {
      transition: all 0.3s ease;
    }
    .payment-method.selected {
      border-color: #3B82F6;
      background-color: #EBF8FF;
    }
  </style>
</head>
<body class="bg-gray-50 min-h-screen">
  <!-- 顶部导航栏 -->
  <nav class="fixed top-0 w-full bg-white z-50 px-4 py-3 border-b border-gray-100">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <button onclick="goBack()" class="w-8 h-8 flex items-center justify-center mr-3 cursor-pointer">
          <i class="ri-arrow-left-line text-gray-600 text-lg"></i>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">确认支付</h1>
      </div>
      <div class="flex items-center space-x-2">
        <button onclick="shareApp()" class="w-8 h-8 flex items-center justify-center">
          <div class="flex space-x-0.5">
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
          </div>
        </button>
        <button class="w-8 h-8 flex items-center justify-center">
          <div class="w-5 h-5 border-2 border-gray-600 rounded-full flex items-center justify-center">
            <div class="w-2 h-2 bg-gray-600 rounded-full"></div>
          </div>
        </button>
      </div>
    </div>
  </nav>

  <!-- 主要内容区域 -->
  <main class="pt-16 pb-20 px-4">
    <!-- 订单信息 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <h3 class="font-semibold text-gray-900 mb-4">订单信息</h3>
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <div id="plan-icon" class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mr-3">
              <i class="ri-vip-crown-2-line text-white text-xl"></i>
            </div>
            <div>
              <h4 id="plan-name" class="font-medium text-gray-900">高级会员</h4>
              <p id="plan-desc" class="text-sm text-gray-500">1个月订阅</p>
            </div>
          </div>
          <div class="text-right">
            <div id="plan-price" class="text-xl font-bold text-gray-900">¥99</div>
            <div class="text-sm text-gray-500 line-through">¥199</div>
          </div>
        </div>
        
        <!-- 优惠信息 -->
        <div class="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
          <div class="flex items-center">
            <i class="ri-fire-line text-red-500 mr-2"></i>
            <span class="text-sm text-red-700 font-medium">限时优惠：首月5折</span>
          </div>
        </div>
        
        <!-- 价格明细 -->
        <div class="space-y-2 pt-4 border-t border-gray-100">
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">商品价格</span>
            <span id="original-price" class="text-gray-900">¥199.00</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">优惠折扣</span>
            <span class="text-red-600">-¥100.00</span>
          </div>
          <div class="flex justify-between text-lg font-bold pt-2 border-t border-gray-100">
            <span>实付金额</span>
            <span id="final-price" class="text-red-600">¥99.00</span>
          </div>
        </div>
      </div>
    </section>

    <!-- 支付方式 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <h3 class="font-semibold text-gray-900 mb-4">支付方式</h3>
        <div class="bg-green-50 border-2 border-green-200 rounded-lg p-4">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mr-4">
              <i class="ri-wechat-pay-line text-white text-xl"></i>
            </div>
            <div>
              <div class="font-medium text-gray-900 text-lg">微信支付</div>
              <div class="text-sm text-gray-600">安全便捷的小程序支付</div>
            </div>
          </div>
        </div>
        <div class="mt-3 text-center text-sm text-gray-500">
          <i class="ri-shield-check-line text-green-500 mr-1"></i>
          微信官方安全保障
        </div>
      </div>
    </section>

    <!-- 服务协议 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="flex items-start">
          <input type="checkbox" id="agree-terms" class="mt-1 mr-3" checked>
          <div class="text-sm text-gray-600">
            我已阅读并同意
            <a href="免责声明.html" class="text-primary">《服务协议》</a>
            和
            <a href="隐私政策.html" class="text-primary">《隐私政策》</a>
            ，确认订阅会员服务
          </div>
        </div>
      </div>
    </section>

    <!-- 会员权益提醒 -->
    <section class="mb-6">
      <div class="bg-blue-50 rounded-xl p-4">
        <h3 class="font-semibold text-gray-900 mb-3">
          <i class="ri-information-line text-blue-500 mr-2"></i>会员权益说明
        </h3>
        <div class="space-y-2 text-sm text-gray-700">
          <div class="flex items-center">
            <i class="ri-check-line text-green-500 mr-2"></i>
            <span>订阅成功后立即生效，可享受所有会员功能</span>
          </div>
          <div class="flex items-center">
            <i class="ri-check-line text-green-500 mr-2"></i>
            <span>支持随时取消，取消后仍可使用至当前周期结束</span>
          </div>
          <div class="flex items-center">
            <i class="ri-check-line text-green-500 mr-2"></i>
            <span>7天无理由退款，不满意可申请全额退款</span>
          </div>
        </div>
      </div>
    </section>

    <!-- 支付按钮 -->
    <section class="mb-6">
      <button id="pay-button" onclick="processPay()" class="w-full py-4 bg-primary text-white rounded-xl font-medium text-lg hover:bg-blue-600 transition-colors">
        立即支付 ¥<span id="pay-amount">99</span>
      </button>
    </section>

    <!-- 安全提示 -->
    <section class="mb-6">
      <div class="text-center text-sm text-gray-500">
        <div class="flex items-center justify-center mb-2">
          <i class="ri-shield-check-line text-green-500 mr-1"></i>
          <span>支付安全由第三方平台保障</span>
        </div>
        <div>如有疑问，请联系客服：400-888-9999</div>
      </div>
    </section>
  </main>

  <script>
    let currentPlan = {
      type: 'premium',
      name: '高级会员',
      price: 99,
      originalPrice: 199
    };

    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 从URL参数获取套餐信息
      const urlParams = new URLSearchParams(window.location.search);
      const planType = urlParams.get('type') || 'premium';
      const price = parseInt(urlParams.get('price')) || 99;

      // 更新套餐信息
      updatePlanInfo(planType, price);
    });

    // 更新套餐信息
    function updatePlanInfo(type, price) {
      const planIcon = document.getElementById('plan-icon');
      const planName = document.getElementById('plan-name');
      const planPrice = document.getElementById('plan-price');
      const originalPrice = document.getElementById('original-price');
      const finalPrice = document.getElementById('final-price');
      const payAmount = document.getElementById('pay-amount');
      
      if (type === 'vip') {
        currentPlan = {
          type: 'vip',
          name: 'VIP会员',
          price: 299,
          originalPrice: 599
        };
        planIcon.innerHTML = '<i class="ri-vip-diamond-line text-white text-xl"></i>';
        planIcon.className = 'w-12 h-12 bg-yellow-600 rounded-full flex items-center justify-center mr-3';
      } else {
        currentPlan = {
          type: 'premium',
          name: '高级会员',
          price: 99,
          originalPrice: 199
        };
        planIcon.innerHTML = '<i class="ri-vip-crown-2-line text-white text-xl"></i>';
        planIcon.className = 'w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mr-3';
      }
      
      planName.textContent = currentPlan.name;
      planPrice.textContent = `¥${currentPlan.price}`;
      originalPrice.textContent = `¥${currentPlan.originalPrice}.00`;
      finalPrice.textContent = `¥${currentPlan.price}.00`;
      payAmount.textContent = currentPlan.price;
    }



    // 处理支付
    function processPay() {
      const agreeTerms = document.getElementById('agree-terms').checked;
      
      if (!agreeTerms) {
        alert('请先同意服务协议和隐私政策');
        return;
      }
      
      // 显示支付处理中
      const payButton = document.getElementById('pay-button');
      payButton.disabled = true;
      payButton.textContent = '处理中...';
      
      // 模拟支付处理
      setTimeout(() => {
        // 模拟支付成功
        showPaymentSuccess();
      }, 2000);
    }

    // 显示支付成功
    function showPaymentSuccess() {
      const main = document.querySelector('main');
      main.innerHTML = `
        <div class="flex flex-col items-center justify-center min-h-[60vh] text-center">
          <div class="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mb-6">
            <i class="ri-check-line text-white text-4xl"></i>
          </div>
          <h2 class="text-2xl font-bold text-gray-900 mb-2">支付成功！</h2>
          <p class="text-gray-600 mb-6">恭喜您成为${currentPlan.name}，所有功能已为您开通</p>
          
          <div class="bg-white rounded-xl p-6 shadow-sm mb-6 w-full max-w-sm">
            <div class="text-center">
              <div class="text-lg font-bold text-gray-900 mb-1">${currentPlan.name}</div>
              <div class="text-sm text-gray-500 mb-3">有效期至 ${getExpiryDate()}</div>
              <div class="text-2xl font-bold text-green-600">¥${currentPlan.price}</div>
            </div>
          </div>
          
          <div class="space-y-3 w-full max-w-sm">
            <button onclick="window.location.href='我的.html'" class="w-full py-3 bg-primary text-white rounded-lg font-medium hover:bg-blue-600 transition-colors">
              返回个人中心
            </button>
            <button onclick="window.location.href='index.html'" class="w-full py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors">
              开始使用
            </button>
          </div>
        </div>
      `;
    }

    // 获取到期日期
    function getExpiryDate() {
      const now = new Date();
      const expiry = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30天后
      return expiry.toLocaleDateString('zh-CN');
    }

    function goBack() {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        window.location.href = '会员升级.html';
      }
    }

    function shareApp() {
      if (navigator.share) {
        navigator.share({
          title: '金融智能体 - 会员支付',
          text: '升级会员，享受专业AI投资服务',
          url: window.location.href
        }).catch(err => {
          console.log('分享失败:', err);
        });
      } else {
        if (navigator.clipboard) {
          navigator.clipboard.writeText(window.location.href).then(() => {
            alert('链接已复制到剪贴板');
          });
        }
      }
    }
  </script>
</body>
</html>
