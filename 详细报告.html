<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>详细报告 - 金融智能体</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: "#3B82F6",
            secondary: "#F1F5F9",
          },
        },
      },
    };
  </script>
  <style>
    .gradient-bg { 
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .stock-up { color: #ef4444; }
    .stock-down { color: #22c55e; }
    .report-section {
      border-left: 4px solid #3B82F6;
      padding-left: 16px;
      margin-bottom: 24px;
    }
    .highlight-box {
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border: 1px solid #0ea5e9;
    }
  </style>
</head>
<body class="bg-gray-50 min-h-screen">
  <!-- 顶部导航栏 -->
  <nav class="fixed top-0 w-full bg-white z-50 px-4 py-3 border-b border-gray-100">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <button onclick="goBack()" class="w-8 h-8 flex items-center justify-center mr-3 cursor-pointer">
          <i class="ri-arrow-left-line text-gray-600 text-lg"></i>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">详细报告</h1>
      </div>
      <div class="flex items-center space-x-2">
        <!-- 微信小程序分享按钮 -->
        <button onclick="shareReport()" class="w-8 h-8 flex items-center justify-center">
          <div class="flex space-x-0.5">
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
          </div>
        </button>
        <!-- 微信小程序返回主界面按钮 -->
        <button class="w-8 h-8 flex items-center justify-center">
          <div class="w-5 h-5 border-2 border-gray-600 rounded-full flex items-center justify-center">
            <div class="w-2 h-2 bg-gray-600 rounded-full"></div>
          </div>
        </button>
      </div>
    </div>
  </nav>

  <!-- 主要内容区域 -->
  <main class="pt-16 pb-20 px-4">
    <!-- 报告头部信息 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center space-x-3">
            <h2 id="stock-name" class="text-lg font-bold text-gray-900">贵州茅台</h2>
            <span id="stock-code" class="text-sm text-gray-500">600519</span>
            <span id="report-type" class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">AI日报</span>
          </div>
          <div class="text-right text-xs text-gray-500">
            <div id="report-date">2024-01-15</div>
            <div id="report-time">09:30</div>
          </div>
        </div>
        
        <h3 id="report-title" class="text-lg font-semibold text-gray-900 mb-2">
          AI日报：白酒龙头估值修复机会显现
        </h3>
        
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
              <span id="signal-badge" class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">买入信号</span>
              <span id="target-price" class="text-xs text-gray-500">目标价 ¥1,750</span>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <span id="return-rate" class="text-xs text-green-600 font-medium">+12.5%</span>
          </div>
        </div>
      </div>
    </section>

    <!-- AI投资建议 - 最重要，放在最前面 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="report-section">
          <h4 class="font-semibold text-gray-900 mb-3">
            <i class="ri-lightbulb-line text-green-600 mr-2"></i>AI投资建议
          </h4>
          <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
            <div class="flex items-center justify-between mb-3">
              <span class="text-xl font-bold text-green-600">买入</span>
              <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">AI推荐</span>
            </div>
            <div class="grid grid-cols-2 gap-4 mb-3">
              <div>
                <div class="text-xs text-gray-600 mb-1">目标价位</div>
                <div class="text-lg font-bold text-gray-900">¥1,750</div>
              </div>
              <div>
                <div class="text-xs text-gray-600 mb-1">止损位</div>
                <div class="text-lg font-bold text-red-600">¥1,580</div>
              </div>
            </div>
            <p class="text-sm text-gray-700 mb-3">
              建议投资者在¥1,620-1,650区间分批建仓，预期收益率15-20%。
            </p>
            <div class="bg-white rounded-lg p-3">
              <h5 class="text-sm font-medium text-gray-900 mb-2">操作策略</h5>
              <ul class="space-y-1">
                <li class="flex items-start">
                  <i class="ri-arrow-right-s-line text-green-500 mt-0.5 mr-1"></i>
                  <span class="text-sm text-gray-700">短期：逢低买入，关注¥1,620支撑位</span>
                </li>
                <li class="flex items-start">
                  <i class="ri-arrow-right-s-line text-green-500 mt-0.5 mr-1"></i>
                  <span class="text-sm text-gray-700">中期：持有至春节前后，关注业绩兑现</span>
                </li>
                <li class="flex items-start">
                  <i class="ri-arrow-right-s-line text-green-500 mt-0.5 mr-1"></i>
                  <span class="text-sm text-gray-700">长期：优质白马股，适合长期配置</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 基本面分析 - 第二重要 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="report-section">
          <h4 class="font-semibold text-gray-900 mb-3">
            <i class="ri-building-line text-blue-600 mr-2"></i>基本面分析
          </h4>
          <div class="space-y-4">
            <div class="bg-blue-50 rounded-lg p-4">
              <h5 class="text-sm font-medium text-blue-800 mb-2">核心观点</h5>
              <p class="text-sm text-blue-700 leading-relaxed">
                基于基本面综合分析，贵州茅台当前位置具备较好的投资价值。股价经过前期调整后，估值已回到合理区间，
                叠加春节旺季临近，白酒消费有望迎来高峰期。
              </p>
            </div>

            <div>
              <h5 class="text-sm font-medium text-gray-900 mb-2">财务指标</h5>
              <div class="grid grid-cols-2 gap-3">
                <div class="bg-gray-50 rounded p-2">
                  <div class="text-xs text-gray-500">市盈率(TTM)</div>
                  <div class="text-sm font-medium">28.5倍</div>
                </div>
                <div class="bg-gray-50 rounded p-2">
                  <div class="text-xs text-gray-500">市净率</div>
                  <div class="text-sm font-medium">8.2倍</div>
                </div>
                <div class="bg-gray-50 rounded p-2">
                  <div class="text-xs text-gray-500">ROE</div>
                  <div class="text-sm font-medium">24.8%</div>
                </div>
                <div class="bg-gray-50 rounded p-2">
                  <div class="text-xs text-gray-500">毛利率</div>
                  <div class="text-sm font-medium">91.2%</div>
                </div>
              </div>
            </div>

            <div>
              <h5 class="text-sm font-medium text-gray-900 mb-2">投资亮点</h5>
              <div class="grid grid-cols-1 gap-2">
                <div class="flex items-start">
                  <i class="ri-check-line text-green-500 mt-1 mr-2"></i>
                  <span class="text-sm text-gray-700">估值修复：当前PE估值处于历史中位数以下，具备安全边际</span>
                </div>
                <div class="flex items-start">
                  <i class="ri-check-line text-green-500 mt-1 mr-2"></i>
                  <span class="text-sm text-gray-700">业绩支撑：Q4业绩预期向好，春节旺季销售有望超预期</span>
                </div>
                <div class="flex items-start">
                  <i class="ri-check-line text-green-500 mt-1 mr-2"></i>
                  <span class="text-sm text-gray-700">品牌护城河：高端白酒市场占据主导地位，定价权强劲</span>
                </div>
                <div class="flex items-start">
                  <i class="ri-check-line text-green-500 mt-1 mr-2"></i>
                  <span class="text-sm text-gray-700">现金流充沛：经营性现金流稳定，分红政策持续</span>
                </div>
              </div>
            </div>

            <div>
              <h5 class="text-sm font-medium text-gray-900 mb-2">行业地位</h5>
              <p class="text-sm text-gray-700 leading-relaxed">
                作为白酒行业绝对龙头，茅台在高端白酒市场占据主导地位，品牌护城河深厚，
                定价权强劲。随着消费升级趋势延续，高端白酒需求保持稳定增长。
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 技术分析 - 第三重要 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="report-section">
          <h4 class="font-semibold text-gray-900 mb-3">
            <i class="ri-line-chart-line text-purple-600 mr-2"></i>技术分析
          </h4>
          <div class="bg-purple-50 rounded-lg p-4 mb-4">
            <h5 class="text-sm font-medium text-purple-800 mb-2">技术面总结</h5>
            <p class="text-sm text-purple-700 leading-relaxed">
              技术指标整体向好，MACD金叉确认上涨趋势，成交量放大显示资金关注度提升。
              突破关键阻力位后，短期有望继续上行。
            </p>
          </div>

          <div class="grid grid-cols-2 gap-4 mb-4">
            <div class="bg-gray-50 rounded-lg p-3">
              <div class="text-xs text-gray-500 mb-1">关键支撑位</div>
              <div class="text-lg font-bold text-green-600">¥1,620</div>
              <div class="text-xs text-gray-500">跌破需谨慎</div>
            </div>
            <div class="bg-gray-50 rounded-lg p-3">
              <div class="text-xs text-gray-500 mb-1">关键阻力位</div>
              <div class="text-lg font-bold text-red-600">¥1,720</div>
              <div class="text-xs text-gray-500">突破看新高</div>
            </div>
          </div>

          <div class="space-y-3">
            <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
              <div class="flex items-center">
                <span class="text-sm text-gray-700">MACD指标</span>
                <i class="ri-information-line text-gray-400 ml-1 text-xs"></i>
              </div>
              <span class="text-sm text-green-600 font-medium">金叉向上 ↗</span>
            </div>
            <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
              <div class="flex items-center">
                <span class="text-sm text-gray-700">KDJ指标</span>
                <i class="ri-information-line text-gray-400 ml-1 text-xs"></i>
              </div>
              <span class="text-sm text-green-600 font-medium">低位金叉 ↗</span>
            </div>
            <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
              <div class="flex items-center">
                <span class="text-sm text-gray-700">RSI指标</span>
                <i class="ri-information-line text-gray-400 ml-1 text-xs"></i>
              </div>
              <span class="text-sm text-yellow-600 font-medium">中性偏强 →</span>
            </div>
            <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
              <div class="flex items-center">
                <span class="text-sm text-gray-700">成交量</span>
                <i class="ri-information-line text-gray-400 ml-1 text-xs"></i>
              </div>
              <span class="text-sm text-green-600 font-medium">放量上涨 ↗</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 市场情绪 - 第四重要 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="report-section">
          <h4 class="font-semibold text-gray-900 mb-3">
            <i class="ri-emotion-line text-yellow-600 mr-2"></i>市场情绪
          </h4>
          <div class="space-y-4">
            <div class="bg-yellow-50 rounded-lg p-4">
              <h5 class="text-sm font-medium text-yellow-800 mb-2">整体情绪</h5>
              <p class="text-sm text-yellow-700 leading-relaxed">
                市场对白酒板块情绪回暖，机构资金持续流入，北向资金净买入明显。
                春节行情预期推动板块整体表现强势。
              </p>
            </div>

            <div class="space-y-3">
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span class="text-sm text-gray-700">机构关注度</span>
                <div class="flex items-center">
                  <div class="w-16 h-2 bg-gray-200 rounded-full mr-2">
                    <div class="w-12 h-2 bg-green-500 rounded-full"></div>
                  </div>
                  <span class="text-sm font-medium text-green-600">高</span>
                </div>
              </div>

              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span class="text-sm text-gray-700">资金流向</span>
                <div class="flex items-center">
                  <div class="w-16 h-2 bg-gray-200 rounded-full mr-2">
                    <div class="w-10 h-2 bg-blue-500 rounded-full"></div>
                  </div>
                  <span class="text-sm font-medium text-blue-600">净流入</span>
                </div>
              </div>

              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span class="text-sm text-gray-700">市场热度</span>
                <div class="flex items-center">
                  <div class="w-16 h-2 bg-gray-200 rounded-full mr-2">
                    <div class="w-11 h-2 bg-orange-500 rounded-full"></div>
                  </div>
                  <span class="text-sm font-medium text-orange-600">较高</span>
                </div>
              </div>
            </div>

            <div>
              <h5 class="text-sm font-medium text-gray-900 mb-2">关键因素</h5>
              <div class="space-y-2">
                <div class="flex items-start">
                  <i class="ri-arrow-up-line text-green-500 mt-1 mr-2"></i>
                  <span class="text-sm text-gray-700">春节备货需求预期推动板块情绪</span>
                </div>
                <div class="flex items-start">
                  <i class="ri-arrow-up-line text-green-500 mt-1 mr-2"></i>
                  <span class="text-sm text-gray-700">机构调研频繁，关注度持续提升</span>
                </div>
                <div class="flex items-start">
                  <i class="ri-arrow-up-line text-green-500 mt-1 mr-2"></i>
                  <span class="text-sm text-gray-700">北向资金连续净买入，外资看好</span>
                </div>
                <div class="flex items-start">
                  <i class="ri-subtract-line text-yellow-500 mt-1 mr-2"></i>
                  <span class="text-sm text-gray-700">估值偏高担忧仍存，部分资金观望</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 风险提示 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="report-section">
          <h4 class="font-semibold text-gray-900 mb-3">
            <i class="ri-alert-line text-red-600 mr-2"></i>风险提示
          </h4>
          <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="space-y-3">
              <div class="flex items-start">
                <i class="ri-error-warning-line text-red-500 mt-1 mr-2"></i>
                <span class="text-sm text-red-700">宏观经济下行可能影响消费需求，关注经济数据变化</span>
              </div>
              <div class="flex items-start">
                <i class="ri-error-warning-line text-red-500 mt-1 mr-2"></i>
                <span class="text-sm text-red-700">行业竞争加剧，市场份额可能受到冲击</span>
              </div>
              <div class="flex items-start">
                <i class="ri-error-warning-line text-red-500 mt-1 mr-2"></i>
                <span class="text-sm text-red-700">估值偏高风险，短期可能面临调整压力</span>
              </div>
              <div class="flex items-start">
                <i class="ri-error-warning-line text-red-500 mt-1 mr-2"></i>
                <span class="text-sm text-red-700">政策变化风险，关注相关监管政策动向</span>
              </div>
            </div>
            <div class="mt-3 pt-3 border-t border-red-200">
              <p class="text-xs text-red-600">
                投资有风险，入市需谨慎。本报告仅供参考，不构成投资建议。
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>





    <!-- 报告生成信息 -->
    <section class="mb-6">
      <div class="bg-gray-50 rounded-xl p-4">
        <div class="flex items-center justify-between text-xs text-gray-500">
          <div class="flex items-center">
            <i class="ri-robot-line mr-1"></i>
            <span>AI智投分析引擎生成</span>
          </div>
          <div>生成时间：2024-01-15 09:30</div>
        </div>
        <div class="mt-2 text-xs text-gray-400">
          本报告基于公开信息和AI算法分析生成，仅供参考，不构成投资建议。投资有风险，入市需谨慎。
        </div>
      </div>
    </section>
  </main>

  <script>
    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 从URL参数获取报告信息
      const urlParams = new URLSearchParams(window.location.search);
      const stockCode = urlParams.get('code') || '600519';
      const stockName = urlParams.get('name') || '贵州茅台';
      const reportType = urlParams.get('type') || 'daily';
      const reportDate = urlParams.get('date') || '2024-01-15';
      
      // 更新页面内容
      updateReportContent(stockCode, stockName, reportType, reportDate);
    });

    // 更新报告内容
    function updateReportContent(stockCode, stockName, reportType, reportDate) {
      document.getElementById('stock-name').textContent = stockName;
      document.getElementById('stock-code').textContent = stockCode;
      document.getElementById('report-date').textContent = reportDate;
      
      // 根据报告类型设置不同的内容
      const reportTypeMap = {
        'daily': { text: 'AI日报', class: 'bg-blue-100 text-blue-700' },
        'weekly': { text: 'AI周报', class: 'bg-purple-100 text-purple-700' },
        'news': { text: '实时资讯', class: 'bg-green-100 text-green-700' }
      };
      
      const typeInfo = reportTypeMap[reportType] || reportTypeMap['daily'];
      const reportTypeBadge = document.getElementById('report-type');
      reportTypeBadge.textContent = typeInfo.text;
      reportTypeBadge.className = `text-xs px-2 py-1 rounded ${typeInfo.class}`;
      
      // 更新页面标题
      document.title = `${typeInfo.text} - ${stockName} - 金融智能体`;
    }

    // 返回功能
    function goBack() {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        window.location.href = 'index.html';
      }
    }

    // 分享报告
    function shareReport() {
      const stockName = document.getElementById('stock-name').textContent;
      const reportType = document.getElementById('report-type').textContent;
      
      if (navigator.share) {
        navigator.share({
          title: `${reportType} - ${stockName}`,
          text: `查看${stockName}的AI分析报告`,
          url: window.location.href
        }).catch(err => {
          console.log('分享失败:', err);
        });
      } else {
        if (navigator.clipboard) {
          navigator.clipboard.writeText(window.location.href).then(() => {
            alert('报告链接已复制到剪贴板');
          });
        }
      }
    }
  </script>
</body>
</html>
