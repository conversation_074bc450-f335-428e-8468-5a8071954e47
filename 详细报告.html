<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>详细报告 - 金融智能体</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: "#3B82F6",
            secondary: "#F1F5F9",
          },
        },
      },
    };
  </script>
  <style>
    .gradient-bg { 
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .stock-up { color: #ef4444; }
    .stock-down { color: #22c55e; }
    .report-section {
      border-left: 4px solid #3B82F6;
      padding-left: 16px;
      margin-bottom: 24px;
    }
    .highlight-box {
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border: 1px solid #0ea5e9;
    }
  </style>
</head>
<body class="bg-gray-50 min-h-screen">
  <!-- 顶部导航栏 -->
  <nav class="fixed top-0 w-full bg-white z-50 px-4 py-3 border-b border-gray-100">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <button onclick="goBack()" class="w-8 h-8 flex items-center justify-center mr-3 cursor-pointer">
          <i class="ri-arrow-left-line text-gray-600 text-lg"></i>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">详细报告</h1>
      </div>
      <div class="flex items-center space-x-2">
        <!-- 微信小程序分享按钮 -->
        <button onclick="shareReport()" class="w-8 h-8 flex items-center justify-center">
          <div class="flex space-x-0.5">
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
          </div>
        </button>
        <!-- 微信小程序返回主界面按钮 -->
        <button class="w-8 h-8 flex items-center justify-center">
          <div class="w-5 h-5 border-2 border-gray-600 rounded-full flex items-center justify-center">
            <div class="w-2 h-2 bg-gray-600 rounded-full"></div>
          </div>
        </button>
      </div>
    </div>
  </nav>

  <!-- 主要内容区域 -->
  <main class="pt-16 pb-20 px-4">
    <!-- 报告头部信息 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center space-x-3">
            <h2 id="stock-name" class="text-lg font-bold text-gray-900">贵州茅台</h2>
            <span id="stock-code" class="text-sm text-gray-500">600519</span>
            <span id="report-type" class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">AI日报</span>
          </div>
          <div class="text-right text-xs text-gray-500">
            <div id="report-date">2024-01-15</div>
            <div id="report-time">09:30</div>
          </div>
        </div>
        
        <h3 id="report-title" class="text-lg font-semibold text-gray-900 mb-2">
          AI日报：白酒龙头估值修复机会显现
        </h3>
        
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
              <span id="signal-badge" class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">买入信号</span>
              <span id="target-price" class="text-xs text-gray-500">目标价 ¥1,750</span>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <span id="return-rate" class="text-xs text-green-600 font-medium">+12.5%</span>
          </div>
        </div>
      </div>
    </section>

    <!-- AI投资建议 - 最重要，放在最前面 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="report-section">
          <h4 class="font-semibold text-gray-900 mb-3">
            <i class="ri-lightbulb-line text-green-600 mr-2"></i>AI投资建议
          </h4>
          <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
            <div class="flex items-center justify-between mb-3">
              <span class="text-xl font-bold text-green-600">买入</span>
              <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">AI推荐</span>
            </div>
            <div class="grid grid-cols-2 gap-4 mb-3">
              <div>
                <div class="text-xs text-gray-600 mb-1">目标价位</div>
                <div class="text-lg font-bold text-gray-900">¥1,750</div>
              </div>
              <div>
                <div class="text-xs text-gray-600 mb-1">止损位</div>
                <div class="text-lg font-bold text-red-600">¥1,580</div>
              </div>
            </div>
            <p class="text-sm text-gray-700 mb-3">
              建议投资者在¥1,620-1,650区间分批建仓，预期收益率15-20%。
            </p>
            <div class="bg-white rounded-lg p-3">
              <h5 class="text-sm font-medium text-gray-900 mb-2">操作策略</h5>
              <ul class="space-y-1">
                <li class="flex items-start">
                  <i class="ri-arrow-right-s-line text-green-500 mt-0.5 mr-1"></i>
                  <span class="text-sm text-gray-700">短期：逢低买入，关注¥1,620支撑位</span>
                </li>
                <li class="flex items-start">
                  <i class="ri-arrow-right-s-line text-green-500 mt-0.5 mr-1"></i>
                  <span class="text-sm text-gray-700">中期：持有至春节前后，关注业绩兑现</span>
                </li>
                <li class="flex items-start">
                  <i class="ri-arrow-right-s-line text-green-500 mt-0.5 mr-1"></i>
                  <span class="text-sm text-gray-700">长期：优质白马股，适合长期配置</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 基本面分析 - 第二重要 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="report-section">
          <h4 class="font-semibold text-gray-900 mb-3">
            <i class="ri-building-line text-blue-600 mr-2"></i>基本面分析
          </h4>
          <div class="space-y-4">
            <div class="bg-blue-50 rounded-lg p-4">
              <h5 class="text-sm font-medium text-blue-800 mb-2">核心观点</h5>
              <p class="text-sm text-blue-700 leading-relaxed">
                基于基本面综合分析，贵州茅台当前位置具备较好的投资价值。股价经过前期调整后，估值已回到合理区间，
                叠加春节旺季临近，白酒消费有望迎来高峰期。
              </p>
            </div>

            <div>
              <h5 class="text-sm font-medium text-gray-900 mb-2">财务指标</h5>
              <div class="grid grid-cols-2 gap-3">
                <div class="bg-gray-50 rounded p-2">
                  <div class="text-xs text-gray-500">市盈率(TTM)</div>
                  <div class="text-sm font-medium">28.5倍</div>
                </div>
                <div class="bg-gray-50 rounded p-2">
                  <div class="text-xs text-gray-500">市净率</div>
                  <div class="text-sm font-medium">8.2倍</div>
                </div>
                <div class="bg-gray-50 rounded p-2">
                  <div class="text-xs text-gray-500">ROE</div>
                  <div class="text-sm font-medium">24.8%</div>
                </div>
                <div class="bg-gray-50 rounded p-2">
                  <div class="text-xs text-gray-500">毛利率</div>
                  <div class="text-sm font-medium">91.2%</div>
                </div>
              </div>
            </div>

            <div>
              <h5 class="text-sm font-medium text-gray-900 mb-2">投资亮点</h5>
              <div class="grid grid-cols-1 gap-2">
                <div class="flex items-start">
                  <i class="ri-check-line text-green-500 mt-1 mr-2"></i>
                  <span class="text-sm text-gray-700">估值修复：当前PE估值处于历史中位数以下，具备安全边际</span>
                </div>
                <div class="flex items-start">
                  <i class="ri-check-line text-green-500 mt-1 mr-2"></i>
                  <span class="text-sm text-gray-700">业绩支撑：Q4业绩预期向好，春节旺季销售有望超预期</span>
                </div>
                <div class="flex items-start">
                  <i class="ri-check-line text-green-500 mt-1 mr-2"></i>
                  <span class="text-sm text-gray-700">品牌护城河：高端白酒市场占据主导地位，定价权强劲</span>
                </div>
                <div class="flex items-start">
                  <i class="ri-check-line text-green-500 mt-1 mr-2"></i>
                  <span class="text-sm text-gray-700">现金流充沛：经营性现金流稳定，分红政策持续</span>
                </div>
              </div>
            </div>

            <div>
              <h5 class="text-sm font-medium text-gray-900 mb-2">行业地位</h5>
              <p class="text-sm text-gray-700 leading-relaxed">
                作为白酒行业绝对龙头，茅台在高端白酒市场占据主导地位，品牌护城河深厚，
                定价权强劲。随着消费升级趋势延续，高端白酒需求保持稳定增长。
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 技术分析 - 第三重要 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="report-section">
          <h4 class="font-semibold text-gray-900 mb-3">
            <i class="ri-line-chart-line text-purple-600 mr-2"></i>技术分析
          </h4>
          <div class="bg-purple-50 rounded-lg p-4 mb-4">
            <h5 class="text-sm font-medium text-purple-800 mb-2">技术面总结</h5>
            <p class="text-sm text-purple-700 leading-relaxed">
              技术指标整体向好，MACD金叉确认上涨趋势，成交量放大显示资金关注度提升。
              突破关键阻力位后，短期有望继续上行。
            </p>
          </div>

          <div class="grid grid-cols-2 gap-4 mb-4">
            <div class="bg-gray-50 rounded-lg p-3">
              <div class="text-xs text-gray-500 mb-1">关键支撑位</div>
              <div class="text-lg font-bold text-green-600">¥1,620</div>
              <div class="text-xs text-gray-500">跌破需谨慎</div>
            </div>
            <div class="bg-gray-50 rounded-lg p-3">
              <div class="text-xs text-gray-500 mb-1">关键阻力位</div>
              <div class="text-lg font-bold text-red-600">¥1,720</div>
              <div class="text-xs text-gray-500">突破看新高</div>
            </div>
          </div>

          <div class="space-y-3">
            <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
              <div class="flex items-center">
                <span class="text-sm text-gray-700">MACD指标</span>
                <i class="ri-information-line text-gray-400 ml-1 text-xs"></i>
              </div>
              <span class="text-sm text-green-600 font-medium">金叉向上 ↗</span>
            </div>
            <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
              <div class="flex items-center">
                <span class="text-sm text-gray-700">KDJ指标</span>
                <i class="ri-information-line text-gray-400 ml-1 text-xs"></i>
              </div>
              <span class="text-sm text-green-600 font-medium">低位金叉 ↗</span>
            </div>
            <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
              <div class="flex items-center">
                <span class="text-sm text-gray-700">RSI指标</span>
                <i class="ri-information-line text-gray-400 ml-1 text-xs"></i>
              </div>
              <span class="text-sm text-yellow-600 font-medium">中性偏强 →</span>
            </div>
            <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
              <div class="flex items-center">
                <span class="text-sm text-gray-700">成交量</span>
                <i class="ri-information-line text-gray-400 ml-1 text-xs"></i>
              </div>
              <span class="text-sm text-green-600 font-medium">放量上涨 ↗</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 市场情绪 - 第四重要 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="report-section">
          <h4 class="font-semibold text-gray-900 mb-3">
            <i class="ri-emotion-line text-yellow-600 mr-2"></i>市场情绪
          </h4>
          <div class="space-y-4">
            <div class="bg-yellow-50 rounded-lg p-4">
              <h5 class="text-sm font-medium text-yellow-800 mb-2">整体情绪</h5>
              <p class="text-sm text-yellow-700 leading-relaxed">
                市场对白酒板块情绪回暖，机构资金持续流入，北向资金净买入明显。
                春节行情预期推动板块整体表现强势。
              </p>
            </div>

            <div class="space-y-3">
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span class="text-sm text-gray-700">机构关注度</span>
                <div class="flex items-center">
                  <div class="w-16 h-2 bg-gray-200 rounded-full mr-2">
                    <div class="w-12 h-2 bg-green-500 rounded-full"></div>
                  </div>
                  <span class="text-sm font-medium text-green-600">高</span>
                </div>
              </div>

              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span class="text-sm text-gray-700">资金流向</span>
                <div class="flex items-center">
                  <div class="w-16 h-2 bg-gray-200 rounded-full mr-2">
                    <div class="w-10 h-2 bg-blue-500 rounded-full"></div>
                  </div>
                  <span class="text-sm font-medium text-blue-600">净流入</span>
                </div>
              </div>

              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span class="text-sm text-gray-700">市场热度</span>
                <div class="flex items-center">
                  <div class="w-16 h-2 bg-gray-200 rounded-full mr-2">
                    <div class="w-11 h-2 bg-orange-500 rounded-full"></div>
                  </div>
                  <span class="text-sm font-medium text-orange-600">较高</span>
                </div>
              </div>
            </div>

            <div>
              <h5 class="text-sm font-medium text-gray-900 mb-2">关键因素</h5>
              <div class="space-y-2">
                <div class="flex items-start">
                  <i class="ri-arrow-up-line text-green-500 mt-1 mr-2"></i>
                  <span class="text-sm text-gray-700">春节备货需求预期推动板块情绪</span>
                </div>
                <div class="flex items-start">
                  <i class="ri-arrow-up-line text-green-500 mt-1 mr-2"></i>
                  <span class="text-sm text-gray-700">机构调研频繁，关注度持续提升</span>
                </div>
                <div class="flex items-start">
                  <i class="ri-arrow-up-line text-green-500 mt-1 mr-2"></i>
                  <span class="text-sm text-gray-700">北向资金连续净买入，外资看好</span>
                </div>
                <div class="flex items-start">
                  <i class="ri-subtract-line text-yellow-500 mt-1 mr-2"></i>
                  <span class="text-sm text-gray-700">估值偏高担忧仍存，部分资金观望</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 风险提示 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="report-section">
          <h4 class="font-semibold text-gray-900 mb-3">
            <i class="ri-alert-line text-red-600 mr-2"></i>风险提示
          </h4>
          <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="space-y-3">
              <div class="flex items-start">
                <i class="ri-error-warning-line text-red-500 mt-1 mr-2"></i>
                <span class="text-sm text-red-700">宏观经济下行可能影响消费需求，关注经济数据变化</span>
              </div>
              <div class="flex items-start">
                <i class="ri-error-warning-line text-red-500 mt-1 mr-2"></i>
                <span class="text-sm text-red-700">行业竞争加剧，市场份额可能受到冲击</span>
              </div>
              <div class="flex items-start">
                <i class="ri-error-warning-line text-red-500 mt-1 mr-2"></i>
                <span class="text-sm text-red-700">估值偏高风险，短期可能面临调整压力</span>
              </div>
              <div class="flex items-start">
                <i class="ri-error-warning-line text-red-500 mt-1 mr-2"></i>
                <span class="text-sm text-red-700">政策变化风险，关注相关监管政策动向</span>
              </div>
            </div>
            <div class="mt-3 pt-3 border-t border-red-200">
              <p class="text-xs text-red-600">
                投资有风险，入市需谨慎。本报告仅供参考，不构成投资建议。
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>





    <!-- 报告生成信息 -->
    <section class="mb-6">
      <div class="bg-gray-50 rounded-xl p-4">
        <div class="flex items-center justify-between text-xs text-gray-500">
          <div class="flex items-center">
            <i class="ri-robot-line mr-1"></i>
            <span>AI智投分析引擎生成</span>
          </div>
          <div>生成时间：2024-01-15 09:30</div>
        </div>
        <div class="mt-2 text-xs text-gray-400">
          本报告基于公开信息和AI算法分析生成，仅供参考，不构成投资建议。投资有风险，入市需谨慎。
        </div>
      </div>
    </section>
  </main>

  <script>
    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 从URL参数获取报告信息
      const urlParams = new URLSearchParams(window.location.search);
      const stockCode = urlParams.get('code') || '600519';
      const stockName = urlParams.get('name') || '贵州茅台';
      const reportType = urlParams.get('type') || 'daily';
      const reportDate = urlParams.get('date') || '2024-01-15';
      
      // 更新页面内容
      updateReportContent(stockCode, stockName, reportType, reportDate);
    });

    // 更新报告内容
    function updateReportContent(stockCode, stockName, reportType, reportDate) {
      document.getElementById('stock-name').textContent = stockName;
      document.getElementById('stock-code').textContent = stockCode;
      document.getElementById('report-date').textContent = reportDate;

      // 根据报告类型设置不同的内容
      const reportTypeMap = {
        'daily': { text: 'AI日报', class: 'bg-blue-100 text-blue-700' },
        'weekly': { text: 'AI周报', class: 'bg-purple-100 text-purple-700' },
        'news': { text: '实时资讯', class: 'bg-green-100 text-green-700' }
      };

      const typeInfo = reportTypeMap[reportType] || reportTypeMap['daily'];
      const reportTypeBadge = document.getElementById('report-type');
      reportTypeBadge.textContent = typeInfo.text;
      reportTypeBadge.className = `text-xs px-2 py-1 rounded ${typeInfo.class}`;

      // 更新页面标题
      document.title = `${typeInfo.text} - ${stockName} - 金融智能体`;

      // 根据报告类型显示不同的内容
      if (reportType === 'weekly') {
        showWeeklyReportContent(stockCode, stockName);
      } else {
        showDailyReportContent(stockCode, stockName);
      }
    }

    // 返回功能
    function goBack() {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        window.location.href = 'index.html';
      }
    }

    // 分享报告
    function shareReport() {
      const stockName = document.getElementById('stock-name').textContent;
      const reportType = document.getElementById('report-type').textContent;

      if (navigator.share) {
        navigator.share({
          title: `${reportType} - ${stockName}`,
          text: `查看${stockName}的AI分析报告`,
          url: window.location.href
        }).catch(err => {
          console.log('分享失败:', err);
        });
      } else {
        if (navigator.clipboard) {
          navigator.clipboard.writeText(window.location.href).then(() => {
            alert('报告链接已复制到剪贴板');
          });
        }
      }
    }

    // 显示日报内容（当前默认内容）
    function showDailyReportContent(stockCode, stockName) {
      // 日报内容保持当前的结构不变
      // 主要关注当日的技术分析、短期操作建议
      document.getElementById('report-title').textContent = `AI日报：${getDailyTitle(stockCode)}`;
    }

    // 显示周报内容
    function showWeeklyReportContent(stockCode, stockName) {
      // 更新报告标题
      document.getElementById('report-title').textContent = `AI周报：${getWeeklyTitle(stockCode)}`;

      // 替换主要内容区域为周报专用内容
      const mainContent = document.querySelector('main');
      const reportHeader = mainContent.querySelector('section:first-child');

      // 保留头部信息，替换后续内容
      const weeklyContent = generateWeeklyContent(stockCode, stockName);

      // 移除现有的分析内容，保留头部
      const sectionsToRemove = mainContent.querySelectorAll('section:not(:first-child)');
      sectionsToRemove.forEach(section => section.remove());

      // 添加周报内容
      mainContent.insertAdjacentHTML('beforeend', weeklyContent);
    }

    // 获取日报标题
    function getDailyTitle(stockCode) {
      const dailyTitles = {
        '600519': '白酒龙头估值修复机会显现',
        '002594': '新能源汽车销量数据亮眼',
        '300750': '电池技术突破带来估值重估',
        '601318': '保险板块底部区域关注价值'
      };
      return dailyTitles[stockCode] || '技术面分析与短期操作建议';
    }

    // 获取周报标题
    function getWeeklyTitle(stockCode) {
      const weeklyTitles = {
        '600519': '白酒行业景气度回升，龙头企业受益明显',
        '002594': '新能源汽车产业链景气持续，关注龙头配置机会',
        '300750': '储能市场爆发在即，电池龙头长期价值凸显',
        '601318': '金融板块估值修复，保险股配置价值显现'
      };
      return weeklyTitles[stockCode] || '行业趋势分析与中期投资策略';
    }

    // 生成周报内容
    function generateWeeklyContent(stockCode, stockName) {
      return `
        <!-- 周度投资策略 -->
        <section class="mb-6">
          <div class="bg-white rounded-xl p-4 shadow-sm">
            <div class="report-section">
              <h4 class="font-semibold text-gray-900 mb-3">
                <i class="ri-compass-line text-purple-600 mr-2"></i>周度投资策略
              </h4>
              <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-4">
                <div class="flex items-center justify-between mb-3">
                  <span class="text-xl font-bold text-purple-600">${getWeeklyRecommendation(stockCode)}</span>
                  <span class="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">周度策略</span>
                </div>
                <div class="grid grid-cols-2 gap-4 mb-3">
                  <div>
                    <div class="text-xs text-gray-600 mb-1">周度目标区间</div>
                    <div class="text-lg font-bold text-gray-900">${getWeeklyTargetRange(stockCode)}</div>
                  </div>
                  <div>
                    <div class="text-xs text-gray-600 mb-1">配置比例建议</div>
                    <div class="text-lg font-bold text-purple-600">${getWeeklyAllocation(stockCode)}</div>
                  </div>
                </div>
                <p class="text-sm text-gray-700 mb-3">
                  ${getWeeklyStrategy(stockCode)}
                </p>
                <div class="bg-white rounded-lg p-3">
                  <h5 class="text-sm font-medium text-gray-900 mb-2">本周操作重点</h5>
                  <ul class="space-y-1">
                    ${getWeeklyOperationPoints(stockCode)}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- 行业景气度分析 -->
        <section class="mb-6">
          <div class="bg-white rounded-xl p-4 shadow-sm">
            <div class="report-section">
              <h4 class="font-semibold text-gray-900 mb-3">
                <i class="ri-line-chart-line text-blue-600 mr-2"></i>行业景气度分析
              </h4>
              <div class="space-y-4">
                <div class="bg-blue-50 rounded-lg p-4">
                  <h5 class="text-sm font-medium text-blue-800 mb-2">行业趋势判断</h5>
                  <p class="text-sm text-blue-700 leading-relaxed">
                    ${getIndustryTrend(stockCode)}
                  </p>
                </div>

                <div>
                  <h5 class="text-sm font-medium text-gray-900 mb-2">景气度指标</h5>
                  <div class="grid grid-cols-2 gap-3">
                    ${getIndustryMetrics(stockCode)}
                  </div>
                </div>

                <div>
                  <h5 class="text-sm font-medium text-gray-900 mb-2">产业链分析</h5>
                  <div class="space-y-2">
                    ${getIndustryChainAnalysis(stockCode)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- 资金流向与机构观点 -->
        <section class="mb-6">
          <div class="bg-white rounded-xl p-4 shadow-sm">
            <div class="report-section">
              <h4 class="font-semibold text-gray-900 mb-3">
                <i class="ri-funds-line text-green-600 mr-2"></i>资金流向与机构观点
              </h4>
              <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                  <div class="bg-green-50 rounded-lg p-3">
                    <h5 class="text-sm font-medium text-green-800 mb-2">本周资金流向</h5>
                    <div class="text-lg font-bold text-green-600">${getWeeklyFundFlow(stockCode)}</div>
                    <p class="text-xs text-green-700 mt-1">${getWeeklyFundFlowDesc(stockCode)}</p>
                  </div>
                  <div class="bg-orange-50 rounded-lg p-3">
                    <h5 class="text-sm font-medium text-orange-800 mb-2">机构持仓变化</h5>
                    <div class="text-lg font-bold text-orange-600">${getInstitutionalChange(stockCode)}</div>
                    <p class="text-xs text-orange-700 mt-1">${getInstitutionalChangeDesc(stockCode)}</p>
                  </div>
                </div>

                <div>
                  <h5 class="text-sm font-medium text-gray-900 mb-2">券商最新观点</h5>
                  <div class="space-y-2">
                    ${getBrokerViews(stockCode)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- 风险提示与关注要点 -->
        <section class="mb-6">
          <div class="bg-white rounded-xl p-4 shadow-sm">
            <div class="report-section">
              <h4 class="font-semibold text-gray-900 mb-3">
                <i class="ri-alert-line text-red-600 mr-2"></i>风险提示与关注要点
              </h4>
              <div class="space-y-4">
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h5 class="text-sm font-medium text-red-800 mb-2">主要风险点</h5>
                  <ul class="space-y-1">
                    ${getWeeklyRisks(stockCode)}
                  </ul>
                </div>

                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h5 class="text-sm font-medium text-yellow-800 mb-2">本周关注要点</h5>
                  <ul class="space-y-1">
                    ${getWeeklyWatchPoints(stockCode)}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- 下周展望 -->
        <section class="mb-6">
          <div class="bg-white rounded-xl p-4 shadow-sm">
            <div class="report-section">
              <h4 class="font-semibold text-gray-900 mb-3">
                <i class="ri-calendar-line text-indigo-600 mr-2"></i>下周展望
              </h4>
              <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                <h5 class="text-sm font-medium text-indigo-800 mb-2">预期走势</h5>
                <p class="text-sm text-indigo-700 leading-relaxed mb-3">
                  ${getNextWeekOutlook(stockCode)}
                </p>
                <div class="grid grid-cols-2 gap-3">
                  <div class="bg-white rounded p-2">
                    <div class="text-xs text-gray-500">预期区间</div>
                    <div class="text-sm font-medium">${getNextWeekRange(stockCode)}</div>
                  </div>
                  <div class="bg-white rounded p-2">
                    <div class="text-xs text-gray-500">关键事件</div>
                    <div class="text-sm font-medium">${getNextWeekEvents(stockCode)}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      `;
    }

    // 周报数据生成函数
    function getWeeklyRecommendation(stockCode) {
      const recommendations = {
        '600519': '逢低配置',
        '002594': '积极关注',
        '300750': '谨慎乐观',
        '601318': '价值配置'
      };
      return recommendations[stockCode] || '持续观察';
    }

    function getWeeklyTargetRange(stockCode) {
      const ranges = {
        '600519': '¥1,620-1,720',
        '002594': '¥240-270',
        '300750': '¥180-200',
        '601318': '¥40-45'
      };
      return ranges[stockCode] || '待定';
    }

    function getWeeklyAllocation(stockCode) {
      const allocations = {
        '600519': '15-20%',
        '002594': '10-15%',
        '300750': '5-10%',
        '601318': '8-12%'
      };
      return allocations[stockCode] || '5-10%';
    }

    function getWeeklyStrategy(stockCode) {
      const strategies = {
        '600519': '基于白酒行业景气度回升和春节旺季预期，建议在当前位置分批建仓，重点关注¥1,620-1,650的配置区间。',
        '002594': '新能源汽车销量持续超预期，产业链景气度维持高位，建议关注回调后的配置机会，重点关注技术面突破。',
        '300750': '储能市场需求爆发，但短期估值偏高，建议等待更好的配置时点，关注业绩兑现情况。',
        '601318': '保险板块估值处于历史低位，长期配置价值显现，建议逢低分批建仓，关注政策面变化。'
      };
      return strategies[stockCode] || '根据市场情况灵活调整配置策略。';
    }

    function getWeeklyOperationPoints(stockCode) {
      const points = {
        '600519': `
          <li class="flex items-start">
            <i class="ri-arrow-right-s-line text-purple-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-gray-700">关注春节备货情况，重点关注渠道库存变化</span>
          </li>
          <li class="flex items-start">
            <i class="ri-arrow-right-s-line text-purple-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-gray-700">跟踪高端白酒价格走势，关注提价预期</span>
          </li>
          <li class="flex items-start">
            <i class="ri-arrow-right-s-line text-purple-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-gray-700">关注年报预告和业绩指引，评估全年增长预期</span>
          </li>`,
        '002594': `
          <li class="flex items-start">
            <i class="ri-arrow-right-s-line text-purple-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-gray-700">关注月度销量数据，重点关注海外市场表现</span>
          </li>
          <li class="flex items-start">
            <i class="ri-arrow-right-s-line text-purple-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-gray-700">跟踪新车型发布和技术突破，关注竞争优势</span>
          </li>
          <li class="flex items-start">
            <i class="ri-arrow-right-s-line text-purple-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-gray-700">关注产业链供应情况，评估成本压力变化</span>
          </li>`,
        '300750': `
          <li class="flex items-start">
            <i class="ri-arrow-right-s-line text-purple-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-gray-700">关注储能订单落地情况，重点关注海外市场</span>
          </li>
          <li class="flex items-start">
            <i class="ri-arrow-right-s-line text-purple-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-gray-700">跟踪电池技术迭代，关注成本下降空间</span>
          </li>
          <li class="flex items-start">
            <i class="ri-arrow-right-s-line text-purple-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-gray-700">关注原材料价格变化，评估盈利能力变化</span>
          </li>`,
        '601318': `
          <li class="flex items-start">
            <i class="ri-arrow-right-s-line text-purple-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-gray-700">关注保险新规落地情况，评估政策影响</span>
          </li>
          <li class="flex items-start">
            <i class="ri-arrow-right-s-line text-purple-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-gray-700">跟踪利率环境变化，关注投资收益率</span>
          </li>
          <li class="flex items-start">
            <i class="ri-arrow-right-s-line text-purple-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-gray-700">关注新业务价值增长，评估转型效果</span>
          </li>`
      };
      return points[stockCode] || '<li class="flex items-start"><i class="ri-arrow-right-s-line text-purple-500 mt-0.5 mr-1"></i><span class="text-sm text-gray-700">关注基本面变化和技术面信号</span></li>';
    }

    function getIndustryTrend(stockCode) {
      const trends = {
        '600519': '白酒行业经过前期调整后，景气度逐步回升。高端白酒需求韧性较强，春节旺季临近，行业整体呈现复苏态势。龙头企业凭借品牌优势和渠道控制力，有望率先受益于行业复苏。',
        '002594': '新能源汽车行业维持高景气度，全球电动化趋势不可逆转。国内外政策持续支持，技术进步推动成本下降，市场渗透率快速提升。产业链上下游协同发展，龙头企业竞争优势持续扩大。',
        '300750': '储能行业迎来爆发期，新能源配储政策推动需求快速增长。电池技术持续进步，成本下降趋势明确。海外市场需求旺盛，国内企业凭借技术和成本优势，有望获得更大市场份额。',
        '601318': '保险行业处于转型关键期，监管政策趋于稳定，行业发展环境逐步改善。利率环境对投资收益影响较大，但长期看保险需求仍有增长空间，龙头企业转型效果逐步显现。'
      };
      return trends[stockCode] || '行业整体发展稳定，关注政策变化和技术进步对行业的影响。';
    }

    function getIndustryMetrics(stockCode) {
      const metrics = {
        '600519': `
          <div class="bg-gray-50 rounded p-2">
            <div class="text-xs text-gray-500">行业增速</div>
            <div class="text-sm font-medium text-green-600">+8.5%</div>
          </div>
          <div class="bg-gray-50 rounded p-2">
            <div class="text-xs text-gray-500">库存周转</div>
            <div class="text-sm font-medium text-blue-600">改善</div>
          </div>
          <div class="bg-gray-50 rounded p-2">
            <div class="text-xs text-gray-500">价格走势</div>
            <div class="text-sm font-medium text-green-600">稳中有升</div>
          </div>
          <div class="bg-gray-50 rounded p-2">
            <div class="text-xs text-gray-500">渠道库存</div>
            <div class="text-sm font-medium text-orange-600">合理</div>
          </div>`,
        '002594': `
          <div class="bg-gray-50 rounded p-2">
            <div class="text-xs text-gray-500">销量增速</div>
            <div class="text-sm font-medium text-green-600">+35.2%</div>
          </div>
          <div class="bg-gray-50 rounded p-2">
            <div class="text-xs text-gray-500">市场份额</div>
            <div class="text-sm font-medium text-blue-600">扩大</div>
          </div>
          <div class="bg-gray-50 rounded p-2">
            <div class="text-xs text-gray-500">技术领先度</div>
            <div class="text-sm font-medium text-green-600">领先</div>
          </div>
          <div class="bg-gray-50 rounded p-2">
            <div class="text-xs text-gray-500">产能利用率</div>
            <div class="text-sm font-medium text-green-600">85%</div>
          </div>`,
        '300750': `
          <div class="bg-gray-50 rounded p-2">
            <div class="text-xs text-gray-500">订单增速</div>
            <div class="text-sm font-medium text-green-600">+45.8%</div>
          </div>
          <div class="bg-gray-50 rounded p-2">
            <div class="text-xs text-gray-500">技术迭代</div>
            <div class="text-sm font-medium text-blue-600">加速</div>
          </div>
          <div class="bg-gray-50 rounded p-2">
            <div class="text-xs text-gray-500">成本下降</div>
            <div class="text-sm font-medium text-green-600">持续</div>
          </div>
          <div class="bg-gray-50 rounded p-2">
            <div class="text-xs text-gray-500">海外占比</div>
            <div class="text-sm font-medium text-orange-600">提升</div>
          </div>`,
        '601318': `
          <div class="bg-gray-50 rounded p-2">
            <div class="text-xs text-gray-500">保费增速</div>
            <div class="text-sm font-medium text-orange-600">+3.2%</div>
          </div>
          <div class="bg-gray-50 rounded p-2">
            <div class="text-xs text-gray-500">新业务价值</div>
            <div class="text-sm font-medium text-blue-600">回升</div>
          </div>
          <div class="bg-gray-50 rounded p-2">
            <div class="text-xs text-gray-500">投资收益率</div>
            <div class="text-sm font-medium text-green-600">4.8%</div>
          </div>
          <div class="bg-gray-50 rounded p-2">
            <div class="text-xs text-gray-500">偿付能力</div>
            <div class="text-sm font-medium text-green-600">充足</div>
          </div>`
      };
      return metrics[stockCode] || '';
    }

    function getIndustryChainAnalysis(stockCode) {
      const analysis = {
        '600519': `
          <div class="flex items-start">
            <i class="ri-check-line text-green-500 mt-1 mr-2"></i>
            <span class="text-sm text-gray-700">上游：原料供应稳定，成本压力可控</span>
          </div>
          <div class="flex items-start">
            <i class="ri-check-line text-green-500 mt-1 mr-2"></i>
            <span class="text-sm text-gray-700">中游：产能利用率提升，库存结构优化</span>
          </div>
          <div class="flex items-start">
            <i class="ri-check-line text-green-500 mt-1 mr-2"></i>
            <span class="text-sm text-gray-700">下游：渠道去库存基本完成，需求逐步回暖</span>
          </div>`,
        '002594': `
          <div class="flex items-start">
            <i class="ri-check-line text-green-500 mt-1 mr-2"></i>
            <span class="text-sm text-gray-700">上游：电池供应链稳定，成本控制能力强</span>
          </div>
          <div class="flex items-start">
            <i class="ri-check-line text-green-500 mt-1 mr-2"></i>
            <span class="text-sm text-gray-700">中游：产能快速扩张，技术迭代加速</span>
          </div>
          <div class="flex items-start">
            <i class="ri-check-line text-green-500 mt-1 mr-2"></i>
            <span class="text-sm text-gray-700">下游：终端需求旺盛，海外市场拓展顺利</span>
          </div>`,
        '300750': `
          <div class="flex items-start">
            <i class="ri-check-line text-green-500 mt-1 mr-2"></i>
            <span class="text-sm text-gray-700">上游：原材料价格回落，供应链稳定</span>
          </div>
          <div class="flex items-start">
            <i class="ri-check-line text-green-500 mt-1 mr-2"></i>
            <span class="text-sm text-gray-700">中游：技术领先优势明显，产能持续扩张</span>
          </div>
          <div class="flex items-start">
            <i class="ri-check-line text-green-500 mt-1 mr-2"></i>
            <span class="text-sm text-gray-700">下游：储能需求爆发，应用场景不断拓展</span>
          </div>`,
        '601318': `
          <div class="flex items-start">
            <i class="ri-check-line text-green-500 mt-1 mr-2"></i>
            <span class="text-sm text-gray-700">监管环境：政策趋于稳定，行业规范发展</span>
          </div>
          <div class="flex items-start">
            <i class="ri-check-line text-green-500 mt-1 mr-2"></i>
            <span class="text-sm text-gray-700">产品创新：保险+科技融合，服务能力提升</span>
          </div>
          <div class="flex items-start">
            <i class="ri-check-line text-green-500 mt-1 mr-2"></i>
            <span class="text-sm text-gray-700">渠道建设：线上线下协同，客户体验改善</span>
          </div>`
      };
      return analysis[stockCode] || '';
    }

    function getWeeklyFundFlow(stockCode) {
      const flows = {
        '600519': '净流入 ¥8.5亿',
        '002594': '净流入 ¥12.3亿',
        '300750': '净流出 ¥3.2亿',
        '601318': '净流入 ¥5.8亿'
      };
      return flows[stockCode] || '数据待更新';
    }

    function getWeeklyFundFlowDesc(stockCode) {
      const descs = {
        '600519': '机构资金持续流入',
        '002594': '北向资金大幅加仓',
        '300750': '获利回吐压力显现',
        '601318': '价值资金逐步关注'
      };
      return descs[stockCode] || '资金流向中性';
    }

    function getInstitutionalChange(stockCode) {
      const changes = {
        '600519': '持仓增加 2.3%',
        '002594': '持仓增加 4.1%',
        '300750': '持仓减少 1.8%',
        '601318': '持仓增加 1.5%'
      };
      return changes[stockCode] || '持仓稳定';
    }

    function getInstitutionalChangeDesc(stockCode) {
      const descs = {
        '600519': '基金重仓股地位稳固',
        '002594': '新能源主题基金加仓',
        '300750': '部分机构获利了结',
        '601318': '价值型基金关注度提升'
      };
      return descs[stockCode] || '机构态度中性';
    }

    function getBrokerViews(stockCode) {
      const views = {
        '600519': `
          <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
            <span class="text-sm text-gray-700">中信证券：买入，目标价¥1,750</span>
            <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">买入</span>
          </div>
          <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
            <span class="text-sm text-gray-700">招商证券：增持，目标价¥1,720</span>
            <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">增持</span>
          </div>`,
        '002594': `
          <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
            <span class="text-sm text-gray-700">华泰证券：买入，目标价¥280</span>
            <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">买入</span>
          </div>
          <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
            <span class="text-sm text-gray-700">国泰君安：增持，目标价¥270</span>
            <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">增持</span>
          </div>`,
        '300750': `
          <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
            <span class="text-sm text-gray-700">中金公司：中性，目标价¥190</span>
            <span class="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded">中性</span>
          </div>
          <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
            <span class="text-sm text-gray-700">海通证券：增持，目标价¥200</span>
            <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">增持</span>
          </div>`,
        '601318': `
          <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
            <span class="text-sm text-gray-700">平安证券：买入，目标价¥45</span>
            <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">买入</span>
          </div>
          <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
            <span class="text-sm text-gray-700">申万宏源：增持，目标价¥43</span>
            <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">增持</span>
          </div>`
      };
      return views[stockCode] || '';
    }

    function getWeeklyRisks(stockCode) {
      const risks = {
        '600519': `
          <li class="flex items-start">
            <i class="ri-alert-line text-red-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-red-700">宏观经济下行压力可能影响消费需求</span>
          </li>
          <li class="flex items-start">
            <i class="ri-alert-line text-red-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-red-700">行业竞争加剧，市场份额面临挑战</span>
          </li>
          <li class="flex items-start">
            <i class="ri-alert-line text-red-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-red-700">估值偏高，短期调整风险需关注</span>
          </li>`,
        '002594': `
          <li class="flex items-start">
            <i class="ri-alert-line text-red-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-red-700">新能源补贴政策变化风险</span>
          </li>
          <li class="flex items-start">
            <i class="ri-alert-line text-red-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-red-700">原材料价格波动影响成本控制</span>
          </li>
          <li class="flex items-start">
            <i class="ri-alert-line text-red-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-red-700">技术迭代风险，需持续研发投入</span>
          </li>`,
        '300750': `
          <li class="flex items-start">
            <i class="ri-alert-line text-red-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-red-700">估值过高，业绩兑现压力较大</span>
          </li>
          <li class="flex items-start">
            <i class="ri-alert-line text-red-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-red-700">行业竞争激烈，技术领先优势面临挑战</span>
          </li>
          <li class="flex items-start">
            <i class="ri-alert-line text-red-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-red-700">海外市场政策变化风险</span>
          </li>`,
        '601318': `
          <li class="flex items-start">
            <i class="ri-alert-line text-red-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-red-700">利率环境变化影响投资收益</span>
          </li>
          <li class="flex items-start">
            <i class="ri-alert-line text-red-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-red-700">监管政策调整风险</span>
          </li>
          <li class="flex items-start">
            <i class="ri-alert-line text-red-500 mt-0.5 mr-1"></i>
            <span class="text-sm text-red-700">转型效果不及预期风险</span>
          </li>`
      };
      return risks[stockCode] || '';
    }

    function getWeeklyWatchPoints(stockCode) {
      const points = {
        '600519': `
          <li class="flex items-start">
            <i class="ri-eye-line text-yellow-600 mt-0.5 mr-1"></i>
            <span class="text-sm text-yellow-700">关注春节销售数据和渠道反馈</span>
          </li>
          <li class="flex items-start">
            <i class="ri-eye-line text-yellow-600 mt-0.5 mr-1"></i>
            <span class="text-sm text-yellow-700">跟踪年报业绩指引和分红政策</span>
          </li>
          <li class="flex items-start">
            <i class="ri-eye-line text-yellow-600 mt-0.5 mr-1"></i>
            <span class="text-sm text-yellow-700">关注白酒板块整体估值修复进度</span>
          </li>`,
        '002594': `
          <li class="flex items-start">
            <i class="ri-eye-line text-yellow-600 mt-0.5 mr-1"></i>
            <span class="text-sm text-yellow-700">关注月度销量数据和新车型发布</span>
          </li>
          <li class="flex items-start">
            <i class="ri-eye-line text-yellow-600 mt-0.5 mr-1"></i>
            <span class="text-sm text-yellow-700">跟踪海外市场拓展进度</span>
          </li>
          <li class="flex items-start">
            <i class="ri-eye-line text-yellow-600 mt-0.5 mr-1"></i>
            <span class="text-sm text-yellow-700">关注电池技术突破和成本控制</span>
          </li>`,
        '300750': `
          <li class="flex items-start">
            <i class="ri-eye-line text-yellow-600 mt-0.5 mr-1"></i>
            <span class="text-sm text-yellow-700">关注储能订单签约和交付情况</span>
          </li>
          <li class="flex items-start">
            <i class="ri-eye-line text-yellow-600 mt-0.5 mr-1"></i>
            <span class="text-sm text-yellow-700">跟踪新技术产业化进度</span>
          </li>
          <li class="flex items-start">
            <i class="ri-eye-line text-yellow-600 mt-0.5 mr-1"></i>
            <span class="text-sm text-yellow-700">关注原材料价格变化趋势</span>
          </li>`,
        '601318': `
          <li class="flex items-start">
            <i class="ri-eye-line text-yellow-600 mt-0.5 mr-1"></i>
            <span class="text-sm text-yellow-700">关注保险新规实施细则</span>
          </li>
          <li class="flex items-start">
            <i class="ri-eye-line text-yellow-600 mt-0.5 mr-1"></i>
            <span class="text-sm text-yellow-700">跟踪新业务价值增长情况</span>
          </li>
          <li class="flex items-start">
            <i class="ri-eye-line text-yellow-600 mt-0.5 mr-1"></i>
            <span class="text-sm text-yellow-700">关注投资收益率变化</span>
          </li>`
      };
      return points[stockCode] || '';
    }

    function getNextWeekOutlook(stockCode) {
      const outlooks = {
        '600519': '预计下周将延续震荡上行态势，重点关注春节备货情况对股价的催化作用。技术面上，如能突破¥1,680阻力位，有望开启新一轮上涨行情。',
        '002594': '下周关注月度销量数据发布，预期数据亮眼将推动股价继续上行。技术面上，¥250关键阻力位突破后，目标看向¥270区域。',
        '300750': '下周可能面临获利回吐压力，建议关注¥180支撑位表现。如有新的储能订单公布，将为股价提供支撑。短期以震荡为主。',
        '601318': '下周预计延续缓慢上行趋势，关注保险新规落地情况。如政策面有积极信号，有望推动估值修复加速。'
      };
      return outlooks[stockCode] || '下周走势有待观察，建议关注基本面变化。';
    }

    function getNextWeekRange(stockCode) {
      const ranges = {
        '600519': '¥1,650-1,720',
        '002594': '¥245-275',
        '300750': '¥180-195',
        '601318': '¥41-44'
      };
      return ranges[stockCode] || '待定';
    }

    function getNextWeekEvents(stockCode) {
      const events = {
        '600519': '春节备货数据',
        '002594': '月度销量发布',
        '300750': '储能订单公告',
        '601318': '保险新规细则'
      };
      return events[stockCode] || '无重大事件';
    }
  </script>
</body>
</html>
