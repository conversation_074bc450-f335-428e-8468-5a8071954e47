<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>自选股 - 金融智能体</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
    <style>
      .tab-active { color: #2563eb; border-bottom: 2px solid #2563eb; }
      .tab-inactive { color: #333; }
      .stock-row { transition: background 0.2s; }
      .stock-row:hover { background: #f5f7fa; }
      .add-btn { color: #2563eb; }
      .bottom-bar-active { color: #2563eb; }
      .bottom-bar-inactive { color: #888; }
    </style>
  </head>
  <body class="bg-[#f7f8fa] pb-16">
    <!-- 顶部导航栏 -->
    <nav class="fixed top-0 w-full bg-white z-50 px-4 py-3 border-b border-gray-100">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <button onclick="goBack()" class="w-8 h-8 flex items-center justify-center mr-3 cursor-pointer">
            <i class="ri-arrow-left-line text-gray-600 text-lg"></i>
          </button>
          <h1 class="text-lg font-semibold text-gray-900">自选股</h1>
        </div>
        <div class="flex items-center space-x-2">
          <!-- 智能体分析按钮 -->
          <button onclick="window.location.href='智能体分析.html'" class="w-8 h-8 flex items-center justify-center">
            <i class="ri-robot-line text-gray-600 text-lg"></i>
          </button>
          <!-- 微信小程序分享按钮 -->
          <button onclick="shareApp()" class="w-8 h-8 flex items-center justify-center">
            <div class="flex space-x-0.5">
              <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
              <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
              <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            </div>
          </button>
          <!-- 微信小程序返回主界面按钮 -->
          <button class="w-8 h-8 flex items-center justify-center">
            <div class="w-5 h-5 border-2 border-gray-600 rounded-full flex items-center justify-center">
              <div class="w-2 h-2 bg-gray-600 rounded-full"></div>
            </div>
          </button>
        </div>
      </div>
    </nav>
    <!-- 自选股标题 -->
    <div class="bg-white px-4 pt-4 pb-3 shadow-sm">
      <div class="flex items-center justify-between">
        <h2 class="text-lg font-semibold text-gray-900">我的自选股</h2>
        <span class="text-xs bg-primary text-white px-2 py-1 rounded-full">8只</span>
      </div>
    </div>
    <!-- 股票列表 -->
    <div class="bg-white mt-2 mx-2 rounded-lg shadow-sm overflow-hidden">
      <div class="flex px-4 py-2 text-xs text-gray-400 border-b">
        <div class="flex-1">股票</div>
        <div class="w-24 text-right">最新价</div>
        <div class="w-20 text-right">涨跌幅</div>
      </div>
      <div class="stock-row flex items-center px-4 py-3 border-b cursor-pointer hover:bg-gray-50">
        <div class="flex-1" onclick="window.location.href='股票分析.html?code=600519&name=贵州茅台'">
          <div class="flex items-center">
            <span class="text-base text-gray-900 font-medium">贵州茅台</span>
            <span class="text-xs text-gray-400 ml-2">600519</span>
          </div>
          <div class="flex items-center space-x-2 mt-1">
            <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">AI建议：持有</span>
          </div>
        </div>
        <div class="w-24 text-right">
          <div class="text-base text-gray-900">1678.50</div>
          <div class="text-xs text-green-600">模拟 +12.5%</div>
        </div>
        <div class="w-20 text-right">
          <span class="bg-red-500 text-white px-2 py-1 rounded text-xs">+2.35%</span>
        </div>
      </div>
      <div class="stock-row flex items-center px-4 py-3 border-b cursor-pointer hover:bg-gray-50">
        <div class="flex-1" onclick="window.location.href='股票分析.html?code=300750&name=宁德时代'">
          <div class="flex items-center">
            <span class="text-base text-gray-900 font-medium">宁德时代</span>
            <span class="text-xs text-gray-400 ml-2">300750</span>
          </div>
          <div class="flex items-center space-x-2 mt-1">
            <span class="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded">AI建议：观望</span>
          </div>
        </div>
        <div class="w-24 text-right">
          <div class="text-base text-gray-900">185.20</div>
          <div class="text-xs text-red-600">模拟 -2.1%</div>
        </div>
        <div class="w-20 text-right">
          <span class="bg-red-500 text-white px-2 py-1 rounded text-xs">+1.85%</span>
        </div>
      </div>
      <div class="stock-row flex items-center px-4 py-3 border-b cursor-pointer hover:bg-gray-50">
        <div class="flex-1" onclick="window.location.href='股票分析.html?code=002594&name=比亚迪'">
          <div class="flex items-center">
            <span class="text-base text-gray-900 font-medium">比亚迪</span>
            <span class="text-xs text-gray-400 ml-2">002594</span>
          </div>
          <div class="flex items-center space-x-2 mt-1">
            <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">AI建议：加仓</span>
          </div>
        </div>
        <div class="w-24 text-right">
          <div class="text-base text-gray-900">245.80</div>
          <div class="text-xs text-green-600">模拟 +8.2%</div>
        </div>
        <div class="w-20 text-right">
          <span class="bg-red-500 text-white px-2 py-1 rounded text-xs">+3.20%</span>
        </div>
      </div>


    

      <!-- 支持的产品类型提示 -->
      <div id="product-types-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
        <div class="bg-white rounded-xl p-6 mx-4 w-full max-w-sm">
          <h3 class="font-semibold text-gray-900 mb-4">支持的金融产品</h3>
          <div class="space-y-2 text-sm">
            <div class="flex items-center">
              <i class="ri-stock-line text-blue-500 mr-2"></i>
              <span>A股、港股、美股</span>
            </div>
            <div class="flex items-center">
              <i class="ri-line-chart-line text-green-500 mr-2"></i>
              <span>上证指数、深证成指等指数</span>
            </div>
            <div class="flex items-center">
              <i class="ri-funds-line text-purple-500 mr-2"></i>
              <span>ETF基金</span>
            </div>
            <div class="flex items-center">
              <i class="ri-coin-line text-yellow-500 mr-2"></i>
              <span>黄金、白银等贵金属</span>
            </div>
            <div class="flex items-center">
              <i class="ri-bar-chart-line text-red-500 mr-2"></i>
              <span>期货合约</span>
            </div>
          </div>
          <button onclick="closeProductTypesModal()" class="w-full mt-4 py-2 bg-primary text-white rounded-lg text-sm">
            知道了
          </button>
        </div>
      </div>
    </div>
    <!-- 底部导航栏 -->
     <nav
      class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50"
    >
      <div class="grid grid-cols-4 h-16">
        <button onclick="window.location.href='index.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-apps-2-line text-lg text-gray-500"></i>
          </div>
          <span class="text-xs text-gray-500">首页</span>
        </button>
        <button onclick="window.location.href='自选股.html'"
          class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item"
        >
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-star-line text-lg text-primary"></i>
          </div>
          <span class="text-xs text-primary">自选股</span>
        </button>
        <button onclick="window.location.href='reports.html'"
          class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item"
        >
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-file-list-line text-lg text-gray-500"></i>
          </div>
          <span class="text-xs text-gray-500">报告中心</span>
        </button>
        <button onclick="window.location.href='我的.html'"
          class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item"
        >
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-user-line text-lg text-gray-500"></i>
          </div>
          <span class="text-xs text-gray-500">我的</span>
        </button>
      </div>
    </nav>

    <script>


      // 显示支持的产品类型
      function showProductTypes() {
        document.getElementById('product-types-modal').classList.remove('hidden');
      }

      // 关闭产品类型弹窗
      function closeProductTypesModal() {
        document.getElementById('product-types-modal').classList.add('hidden');
      }

      // 底部导航栏交互
      document.addEventListener("DOMContentLoaded", function () {
        const tabItems = document.querySelectorAll(".tab-item");
        tabItems.forEach((item) => {
          item.addEventListener("click", function () {
            tabItems.forEach((tab) => {
              const icon = tab.querySelector("i");
              const text = tab.querySelector("span");
              icon.classList.remove("text-primary");
              icon.classList.add("text-gray-500");
              text.classList.remove("text-primary", "font-medium");
              text.classList.add("text-gray-500");
              tab.classList.remove("active");
            });
            const icon = this.querySelector("i");
            const text = this.querySelector("span");
            icon.classList.remove("text-gray-500");
            icon.classList.add("text-primary");
            text.classList.remove("text-gray-500");
            text.classList.add("text-primary", "font-medium");
            this.classList.add("active");
          });
        });
      });

      // 返回功能
      window.goBack = function() {
        if (window.history.length > 1) {
          window.history.back();
        } else {
          window.location.href = 'index.html';
        }
      };

      // 分享功能
      window.shareApp = function() {
        if (navigator.share) {
          navigator.share({
            title: '金融智能体 - 自选股',
            text: '查看我的自选股票',
            url: window.location.href
          }).catch(err => {
            console.log('分享失败:', err);
          });
        } else {
          // 复制链接
          if (navigator.clipboard) {
            navigator.clipboard.writeText(window.location.href).then(() => {
              alert('链接已复制到剪贴板');
            });
          }
        }
      };
    </script>

  </body>
</html>