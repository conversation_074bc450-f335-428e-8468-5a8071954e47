<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>消息中心 - 金融智能体</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: "#3B82F6",
            secondary: "#F1F5F9",
          },
        },
      },
    };
  </script>
</head>
<body class="bg-gray-50 min-h-screen">
  <!-- 顶部导航栏 -->
  <nav class="fixed top-0 w-full bg-white z-50 px-4 py-3 border-b border-gray-100">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <button onclick="goBack()" class="w-8 h-8 flex items-center justify-center mr-3 cursor-pointer">
          <i class="ri-arrow-left-line text-gray-600 text-lg"></i>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">消息中心</h1>
      </div>
      <div class="flex items-center space-x-2">
        <button onclick="markAllRead()" class="text-sm text-primary">全部已读</button>
        <button class="w-8 h-8 flex items-center justify-center">
          <div class="w-5 h-5 border-2 border-gray-600 rounded-full flex items-center justify-center">
            <div class="w-2 h-2 bg-gray-600 rounded-full"></div>
          </div>
        </button>
      </div>
    </div>
  </nav>

  <!-- 主要内容区域 -->
  <main class="pt-16 pb-20 px-4">
    <!-- 消息分类 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="grid grid-cols-4 gap-3">
          <button onclick="filterMessages('all')" class="message-filter active flex flex-col items-center p-3 rounded-lg">
            <i class="ri-message-3-line text-xl mb-1"></i>
            <span class="text-xs">全部</span>
            <span class="text-xs text-red-500 font-bold">3</span>
          </button>
          
          <button onclick="filterMessages('ai')" class="message-filter flex flex-col items-center p-3 rounded-lg">
            <i class="ri-robot-line text-xl mb-1"></i>
            <span class="text-xs">AI提醒</span>
            <span class="text-xs text-red-500 font-bold">2</span>
          </button>
          
          <button onclick="filterMessages('trade')" class="message-filter flex flex-col items-center p-3 rounded-lg">
            <i class="ri-line-chart-line text-xl mb-1"></i>
            <span class="text-xs">交易</span>
            <span class="text-xs text-red-500 font-bold">1</span>
          </button>
          
          <button onclick="filterMessages('system')" class="message-filter flex flex-col items-center p-3 rounded-lg">
            <i class="ri-settings-line text-xl mb-1"></i>
            <span class="text-xs">系统</span>
          </button>
        </div>
      </div>
    </section>

    <!-- 消息列表 -->
    <section class="mb-6">
      <div class="space-y-3">
        <!-- AI提醒消息 -->
        <div class="message-item bg-white rounded-xl p-4 shadow-sm" data-category="ai" data-read="false">
          <div class="flex items-start space-x-3">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
              <i class="ri-robot-line text-blue-600"></i>
            </div>
            <div class="flex-1">
              <div class="flex items-center justify-between mb-1">
                <h3 class="font-medium text-gray-900">AI交易提醒</h3>
                <div class="flex items-center space-x-2">
                  <span class="text-xs text-gray-500">2分钟前</span>
                  <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                </div>
              </div>
              <p class="text-sm text-gray-600 mb-2">
                贵州茅台(600519)触发AI买入信号，建议价格¥1,650-1,660区间加仓
              </p>
              <div class="flex items-center space-x-2">
                <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">AI建议</span>
                <button onclick="viewStock('600519')" class="text-xs text-primary">查看详情</button>
              </div>
            </div>
          </div>
        </div>

        <!-- 交易消息 -->
        <div class="message-item bg-white rounded-xl p-4 shadow-sm" data-category="trade" data-read="false">
          <div class="flex items-start space-x-3">
            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
              <i class="ri-line-chart-line text-green-600"></i>
            </div>
            <div class="flex-1">
              <div class="flex items-center justify-between mb-1">
                <h3 class="font-medium text-gray-900">模拟交易成功</h3>
                <div class="flex items-center space-x-2">
                  <span class="text-xs text-gray-500">1小时前</span>
                  <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                </div>
              </div>
              <p class="text-sm text-gray-600 mb-2">
                AI自动买入比亚迪(002594) 100股，成交价¥245.67
              </p>
              <div class="flex items-center space-x-2">
                <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">买入成功</span>
                <button onclick="viewTrade('002594')" class="text-xs text-primary">查看持仓</button>
              </div>
            </div>
          </div>
        </div>

        <!-- AI分析消息 -->
        <div class="message-item bg-white rounded-xl p-4 shadow-sm" data-category="ai" data-read="false">
          <div class="flex items-start space-x-3">
            <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
              <i class="ri-file-text-line text-purple-600"></i>
            </div>
            <div class="flex-1">
              <div class="flex items-center justify-between mb-1">
                <h3 class="font-medium text-gray-900">AI分析报告</h3>
                <div class="flex items-center space-x-2">
                  <span class="text-xs text-gray-500">3小时前</span>
                  <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                </div>
              </div>
              <p class="text-sm text-gray-600 mb-2">
                您关注的股票今日分析报告已生成，共3只股票有重要更新
              </p>
              <div class="flex items-center space-x-2">
                <span class="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">分析报告</span>
                <button onclick="viewReports()" class="text-xs text-primary">查看报告</button>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统消息 -->
        <div class="message-item bg-white rounded-xl p-4 shadow-sm" data-category="system" data-read="true">
          <div class="flex items-start space-x-3">
            <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
              <i class="ri-settings-line text-gray-600"></i>
            </div>
            <div class="flex-1">
              <div class="flex items-center justify-between mb-1">
                <h3 class="font-medium text-gray-600">系统维护通知</h3>
                <span class="text-xs text-gray-500">昨天</span>
              </div>
              <p class="text-sm text-gray-500 mb-2">
                系统将于今晚23:00-24:00进行维护升级，期间可能影响部分功能使用
              </p>
              <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">系统通知</span>
            </div>
          </div>
        </div>

        <!-- 会员消息 -->
        <div class="message-item bg-white rounded-xl p-4 shadow-sm" data-category="system" data-read="true">
          <div class="flex items-start space-x-3">
            <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0">
              <i class="ri-vip-crown-line text-yellow-600"></i>
            </div>
            <div class="flex-1">
              <div class="flex items-center justify-between mb-1">
                <h3 class="font-medium text-gray-600">会员优惠活动</h3>
                <span class="text-xs text-gray-500">2天前</span>
              </div>
              <p class="text-sm text-gray-500 mb-2">
                限时优惠！升级高级会员享5折优惠，更多AI功能等你体验
              </p>
              <div class="flex items-center space-x-2">
                <span class="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded">会员优惠</span>
                <button onclick="upgradeVip()" class="text-xs text-primary">立即升级</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 空状态 -->
    <div id="emptyState" class="hidden text-center py-12">
      <i class="ri-message-3-line text-4xl text-gray-300 mb-4"></i>
      <p class="text-gray-500">暂无相关消息</p>
    </div>
  </main>

  <script>
    let currentFilter = 'all';

    function goBack() {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        window.location.href = '我的.html';
      }
    }

    function filterMessages(category) {
      currentFilter = category;
      
      // 更新按钮状态
      document.querySelectorAll('.message-filter').forEach(btn => {
        btn.classList.remove('active', 'bg-primary', 'text-white');
        btn.classList.add('text-gray-600');
      });
      
      event.target.closest('.message-filter').classList.add('active', 'bg-primary', 'text-white');
      event.target.closest('.message-filter').classList.remove('text-gray-600');
      
      // 过滤消息
      const messages = document.querySelectorAll('.message-item');
      let visibleCount = 0;
      
      messages.forEach(msg => {
        const msgCategory = msg.getAttribute('data-category');
        if (category === 'all' || msgCategory === category) {
          msg.style.display = 'block';
          visibleCount++;
        } else {
          msg.style.display = 'none';
        }
      });
      
      // 显示空状态
      const emptyState = document.getElementById('emptyState');
      if (visibleCount === 0) {
        emptyState.classList.remove('hidden');
      } else {
        emptyState.classList.add('hidden');
      }
    }

    function markAllRead() {
      document.querySelectorAll('.message-item').forEach(msg => {
        msg.setAttribute('data-read', 'true');
        const dot = msg.querySelector('.bg-red-500');
        if (dot) {
          dot.style.display = 'none';
        }
        // 更改文字颜色为已读状态
        const title = msg.querySelector('h3');
        title.classList.remove('text-gray-900');
        title.classList.add('text-gray-600');
      });
      
      // 更新分类按钮的未读数量
      document.querySelectorAll('.message-filter .text-red-500').forEach(badge => {
        badge.textContent = '0';
        badge.style.display = 'none';
      });
      
      alert('所有消息已标记为已读');
    }

    function viewStock(code) {
      window.location.href = `个股模拟盘.html?code=${code}`;
    }

    function viewTrade(code) {
      window.location.href = `个股模拟盘.html?code=${code}`;
    }

    function viewReports() {
      window.location.href = 'reports.html';
    }

    function upgradeVip() {
      window.location.href = '会员升级.html';
    }

    // 页面加载时设置默认状态
    document.addEventListener('DOMContentLoaded', function() {
      // 设置默认选中状态
      document.querySelector('.message-filter').classList.add('active', 'bg-primary', 'text-white');
    });
  </script>
</body>
</html>
