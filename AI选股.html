<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>今日推荐 - 金融智能体</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#3B82F6",
              secondary: "#F1F5F9",
            },
            animation: {
              'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
              'bounce-slow': 'bounce 2s infinite',
            }
          },
        },
      };
    </script>
    <style>
      .stock-up { color: #ef4444; }
      .stock-down { color: #10b981; }
      .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
      .ai-card {
        background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1), 0 4px 10px rgba(0,0,0,0.05);
      }
      .chat-bubble {
        animation: slideInUp 0.5s ease-out;
      }
      @keyframes slideInUp {
        from { transform: translateY(20px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
      }
      .typing-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #3B82F6;
        animation: typing 1.4s infinite ease-in-out;
      }
      .typing-indicator:nth-child(1) { animation-delay: -0.32s; }
      .typing-indicator:nth-child(2) { animation-delay: -0.16s; }
      @keyframes typing {
        0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
        40% { transform: scale(1); opacity: 1; }
      }
    </style>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <nav class="fixed top-0 w-full bg-white z-50 px-4 py-3 border-b border-gray-100">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <button onclick="goBack()" class="w-8 h-8 flex items-center justify-center mr-3 cursor-pointer">
            <i class="ri-arrow-left-line text-gray-600 text-lg"></i>
          </button>
          <h1 class="text-lg font-semibold text-gray-900">AI今日推荐</h1>
        </div>
        <div class="flex items-center space-x-2">
          <!-- 搜索按钮 -->
          <button onclick="openSearch()" class="w-8 h-8 flex items-center justify-center">
            <i class="ri-search-line text-gray-600 text-lg"></i>
          </button>
          <!-- 消息中心按钮 -->
          <button onclick="openMessageCenter()" class="w-8 h-8 flex items-center justify-center relative">
            <i class="ri-notification-line text-gray-600 text-lg"></i>
            <!-- 未读消息提示点 -->
            <div class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
          </button>
          <!-- 微信小程序分享按钮 -->
          <button onclick="shareApp()" class="w-8 h-8 flex items-center justify-center">
            <div class="flex space-x-0.5">
              <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
              <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
              <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            </div>
          </button>
          <!-- 微信小程序返回主界面按钮 -->
          <button class="w-8 h-8 flex items-center justify-center">
            <div class="w-5 h-5 border-2 border-gray-600 rounded-full flex items-center justify-center">
              <div class="w-2 h-2 bg-gray-600 rounded-full"></div>
            </div>
          </button>
        </div>
      </div>
    </nav>

    <!-- 主体内容 -->
    <main class="pt-16 pb-20 px-4">
      <!-- 今日推荐概览 -->
      <section class="mb-6">
        <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 border border-blue-100">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
              <i class="ri-robot-line text-primary text-xl"></i>
              <span class="font-semibold text-gray-900">AI今日推荐</span>
              <span class="text-xs bg-primary text-white px-2 py-1 rounded-full">实时更新</span>
            </div>
            <span class="text-xs text-gray-500">2024-07-24 09:30</span>
          </div>

          <div class="grid grid-cols-2 gap-4 text-center">
            <div>
              <div class="text-lg font-bold text-gray-900">5</div>
              <div class="text-xs text-gray-500">今日推荐</div>
            </div>
            <div>
              <div class="text-lg font-bold text-blue-600">+12.3%</div>
              <div class="text-xs text-gray-500">平均收益</div>
            </div>
          </div>
        </div>
      </section>

      <!-- 投资周期选择 -->
      <section class="mb-6">
        <div class="bg-white rounded-xl p-4 shadow-sm">
          <h2 class="font-semibold text-gray-900 mb-3">投资周期</h2>
          <div class="flex space-x-2">
            <button class="investment-period px-4 py-2 bg-primary text-white rounded-lg text-sm font-medium active" data-period="short">短线交易</button>
            <button class="investment-period px-4 py-2 border border-gray-200 bg-white text-gray-700 rounded-lg text-sm font-medium" data-period="long">中长线投资</button>
          </div>
        </div>
      </section>



      <!-- 推荐股票列表 -->
      <section class="mb-6" id="stock-recommendations">
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
          <div class="px-4 py-3 border-b border-gray-100">
            <h3 class="font-medium text-gray-900">推荐股票</h3>
          </div>

          <div class="divide-y divide-gray-100" id="stock-list">
            <!-- 默认显示短线股票 -->
            <div class="px-4 py-4 hover:bg-gray-50 cursor-pointer" onclick="viewStock('002594', '比亚迪')">
              <div class="flex items-start justify-between mb-3">
                <div class="flex-1">
                  <div class="flex items-center space-x-2 mb-2">
                    <span class="font-medium text-gray-900">比亚迪</span>
                    <span class="text-sm text-gray-500">002594</span>
                  </div>
                  <div class="flex items-center space-x-2 mb-2">
                    <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">买入</span>
                    <span class="text-xs text-gray-500">目标价: 280</span>
                    <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">短线</span>
                  </div>
                  <div class="text-xs text-gray-600 leading-relaxed">
                    <i class="ri-lightbulb-line mr-1 text-yellow-500"></i>
                    新能源汽车销量创新高，月销量突破30万辆。技术面突破关键阻力位245元，MACD金叉向上，RSI指标显示强势。短期目标价280元，建议分批买入。
                  </div>
                </div>
                <div class="text-right ml-4">
                  <div class="font-medium text-gray-900">245.80</div>
                  <div class="text-sm text-red-500">+3.25%</div>
                  <button onclick="event.stopPropagation(); addToWatchlist('002594', '比亚迪')" class="mt-2 px-3 py-1 bg-primary text-white text-xs rounded-full hover:bg-blue-600 transition-colors">
                    <i class="ri-star-line mr-1"></i>加自选
                  </button>
                </div>
              </div>
            </div>

            <div class="px-4 py-4 hover:bg-gray-50 cursor-pointer" onclick="viewStock('300750', '宁德时代')">
              <div class="flex items-start justify-between mb-3">
                <div class="flex-1">
                  <div class="flex items-center space-x-2 mb-2">
                    <span class="font-medium text-gray-900">宁德时代</span>
                    <span class="text-sm text-gray-500">300750</span>
                  </div>
                  <div class="flex items-center space-x-2 mb-2">
                    <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">买入</span>
                    <span class="text-xs text-gray-500">目标价: 220</span>
                    <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">短线</span>
                  </div>
                  <div class="text-xs text-gray-600 leading-relaxed">
                    <i class="ri-lightbulb-line mr-1 text-yellow-500"></i>
                    电池技术全球领先，获得特斯拉、宝马等大额订单。三季度业绩超预期，毛利率回升。技术面放量突破，短期有望冲击220元。
                  </div>
                </div>
                <div class="text-right ml-4">
                  <div class="font-medium text-gray-900">198.50</div>
                  <div class="text-sm text-red-500">+2.85%</div>
                  <button onclick="event.stopPropagation(); addToWatchlist('300750', '宁德时代')" class="mt-2 px-3 py-1 bg-primary text-white text-xs rounded-full hover:bg-blue-600 transition-colors">
                    <i class="ri-star-line mr-1"></i>加自选
                  </button>
                </div>
              </div>
            </div>

            <div class="px-4 py-4 hover:bg-gray-50 cursor-pointer" onclick="viewStock('300059', '东方财富')">
              <div class="flex items-start justify-between mb-3">
                <div class="flex-1">
                  <div class="flex items-center space-x-2 mb-2">
                    <span class="font-medium text-gray-900">东方财富</span>
                    <span class="text-sm text-gray-500">300059</span>
                  </div>
                  <div class="flex items-center space-x-2 mb-2">
                    <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">买入</span>
                    <span class="text-xs text-gray-500">目标价: 18</span>
                    <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">短线</span>
                  </div>
                  <div class="text-xs text-gray-600 leading-relaxed">
                    <i class="ri-lightbulb-line mr-1 text-yellow-500"></i>
                    券商板块轮动行情启动，成交量连续3日放大。公司互联网金融业务增长强劲，用户数突破2亿。技术指标全面向好，短期目标18元。
                  </div>
                </div>
                <div class="text-right ml-4">
                  <div class="font-medium text-gray-900">15.68</div>
                  <div class="text-sm text-red-500">+4.12%</div>
                  <button onclick="event.stopPropagation(); addToWatchlist('300059', '东方财富')" class="mt-2 px-3 py-1 bg-primary text-white text-xs rounded-full hover:bg-blue-600 transition-colors">
                    <i class="ri-star-line mr-1"></i>加自选
                  </button>
                </div>
              </div>
            </div>

            <div class="px-4 py-4 hover:bg-gray-50 cursor-pointer" onclick="viewStock('601318', '中国平安')">
              <div class="flex items-start justify-between mb-3">
                <div class="flex-1">
                  <div class="flex items-center space-x-2 mb-2">
                    <span class="font-medium text-gray-900">中国平安</span>
                    <span class="text-sm text-gray-500">601318</span>
                  </div>
                  <div class="flex items-center space-x-2 mb-2">
                    <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">持有</span>
                    <span class="text-xs text-gray-500">目标价: 52</span>
                    <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">短线</span>
                  </div>
                  <div class="text-xs text-gray-600 leading-relaxed">
                    <i class="ri-lightbulb-line mr-1 text-yellow-500"></i>
                    保险业务逐步回暖，新业务价值增长15%。估值处于历史低位，PEV仅0.6倍。技术面底部企稳，反弹空间较大，短期目标52元。
                  </div>
                </div>
                <div class="text-right ml-4">
                  <div class="font-medium text-gray-900">45.20</div>
                  <div class="text-sm text-red-500">+1.95%</div>
                  <button onclick="event.stopPropagation(); addToWatchlist('601318', '中国平安')" class="mt-2 px-3 py-1 bg-primary text-white text-xs rounded-full hover:bg-blue-600 transition-colors">
                    <i class="ri-star-line mr-1"></i>加自选
                  </button>
                </div>
              </div>
            </div>

            <div class="px-4 py-4 hover:bg-gray-50 cursor-pointer" onclick="viewStock('600036', '招商银行')">
              <div class="flex items-start justify-between mb-3">
                <div class="flex-1">
                  <div class="flex items-center space-x-2 mb-2">
                    <span class="font-medium text-gray-900">招商银行</span>
                    <span class="text-sm text-gray-500">600036</span>
                  </div>
                  <div class="flex items-center space-x-2 mb-2">
                    <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">买入</span>
                    <span class="text-xs text-gray-500">目标价: 40</span>
                    <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">短线</span>
                  </div>
                  <div class="text-xs text-gray-600 leading-relaxed">
                    <i class="ri-lightbulb-line mr-1 text-yellow-500"></i>
                    银行股补涨行情，资产质量优异，不良率仅0.91%。ROE保持15%以上，分红收益率4.5%。技术面突破下降趋势线，短期目标40元。
                  </div>
                </div>
                <div class="text-right ml-4">
                  <div class="font-medium text-gray-900">35.80</div>
                  <div class="text-sm text-red-500">+2.15%</div>
                  <button onclick="event.stopPropagation(); addToWatchlist('600036', '招商银行')" class="mt-2 px-3 py-1 bg-primary text-white text-xs rounded-full hover:bg-blue-600 transition-colors">
                    <i class="ri-star-line mr-1"></i>加自选
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- AI今日推荐内容区域 -->
      <section id="ai-picker-content" class="hidden">
        <!-- 推荐策略说明 -->
        <div class="bg-white rounded-xl p-4 mb-4 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-3">AI推荐策略</h3>
          <div class="space-y-3">
            <div class="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
              <i class="ri-brain-line text-blue-600 text-lg mt-0.5"></i>
              <div>
                <h4 class="font-medium text-gray-900 mb-1">多因子量化模型</h4>
                <p class="text-xs text-gray-600">结合技术面、基本面、资金面等50+因子进行综合评分</p>
              </div>
            </div>
            <div class="flex items-start space-x-3 p-3 bg-green-50 rounded-lg">
              <i class="ri-line-chart-line text-green-600 text-lg mt-0.5"></i>
              <div>
                <h4 class="font-medium text-gray-900 mb-1">机器学习算法</h4>
                <p class="text-xs text-gray-600">基于历史数据训练，实时学习市场变化规律</p>
              </div>
            </div>
            <div class="flex items-start space-x-3 p-3 bg-purple-50 rounded-lg">
              <i class="ri-shield-check-line text-purple-600 text-lg mt-0.5"></i>
              <div>
                <h4 class="font-medium text-gray-900 mb-1">风险控制系统</h4>
                <p class="text-xs text-gray-600">智能识别风险信号，过滤高风险标的</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 投资周期选择 -->
        <div class="bg-white rounded-xl p-4 mb-4 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-3">选择投资周期</h3>
          <div class="grid grid-cols-2 gap-3">
            <button class="investment-period active px-4 py-3 rounded-lg border-2 border-primary bg-primary text-white text-sm font-medium transition-all">
              <i class="ri-flashlight-line mr-2"></i>短线投资
              <div class="text-xs opacity-90 mt-1">1-7天</div>
            </button>
            <button class="investment-period px-4 py-3 rounded-lg border-2 border-gray-200 bg-white text-gray-700 text-sm font-medium transition-all">
              <i class="ri-time-line mr-2"></i>中长线投资
              <div class="text-xs opacity-70 mt-1">1-6个月</div>
            </button>
          </div>
        </div>

        <!-- AI推荐股票 -->
        <div class="bg-white rounded-xl p-4 shadow-sm">
          <div class="flex items-center justify-between mb-4">
            <h3 class="font-semibold text-gray-900">AI精选推荐</h3>
            <button id="refresh-stocks" class="text-primary text-sm flex items-center">
              <i class="ri-refresh-line mr-1"></i>刷新
            </button>
          </div>

          <div id="stock-list" class="space-y-4">
            <!-- 推荐股票1 -->
            <div class="border border-gray-200 rounded-lg p-4">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                    <span class="text-red-600 font-bold text-sm">茅</span>
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-900">贵州茅台</h4>
                    <span class="text-sm text-gray-500">600519</span>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-lg font-bold text-gray-900">¥1,678.50</div>
                  <div class="text-sm text-green-600">+2.35%</div>
                </div>
              </div>

              <!-- AI评分 -->
              <div class="grid grid-cols-2 gap-3 mb-3">
                <div class="text-center">
                  <div class="text-lg font-bold text-green-600">92</div>
                  <div class="text-xs text-gray-500">AI评分</div>
                </div>
                <div class="text-center">
                  <div class="text-lg font-bold text-purple-600">+15%</div>
                  <div class="text-xs text-gray-500">预期收益</div>
                </div>
              </div>

              <!-- 推荐理由 -->
              <div class="bg-gray-50 rounded-lg p-3 mb-3">
                <h5 class="font-medium text-gray-900 mb-2">AI推荐理由</h5>
                <div class="space-y-1 text-xs text-gray-600">
                  <div class="flex items-center">
                    <i class="ri-check-line text-green-500 mr-1"></i>
                    <span>技术面：MACD金叉，RSI处于强势区间，成交量放大</span>
                  </div>
                  <div class="flex items-center">
                    <i class="ri-check-line text-green-500 mr-1"></i>
                    <span>基本面：Q2业绩超预期，毛利率持续提升</span>
                  </div>
                  <div class="flex items-center">
                    <i class="ri-check-line text-green-500 mr-1"></i>
                    <span>资金面：北向资金连续3日净流入，机构增持明显</span>
                  </div>
                </div>
              </div>

              <!-- 操作建议 -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">强烈推荐</span>
                  <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">短线</span>
                </div>
                <button onclick="addToWatchlist('600519', '贵州茅台')" class="px-3 py-1 bg-primary text-white text-xs rounded-full hover:bg-blue-600 transition-colors">
                  <i class="ri-star-line mr-1"></i>加自选
                </button>
              </div>
            </div>

            <!-- 推荐股票2 -->
            <div class="border border-gray-200 rounded-lg p-4">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <span class="text-blue-600 font-bold text-sm">招</span>
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-900">招商银行</h4>
                    <span class="text-sm text-gray-500">600036</span>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-lg font-bold text-gray-900">¥32.45</div>
                  <div class="text-sm text-red-600">-0.56%</div>
                </div>
              </div>

              <!-- AI评分 -->
              <div class="grid grid-cols-2 gap-3 mb-3">
                <div class="text-center">
                  <div class="text-lg font-bold text-green-600">88</div>
                  <div class="text-xs text-gray-500">AI评分</div>
                </div>
                <div class="text-center">
                  <div class="text-lg font-bold text-purple-600">+12%</div>
                  <div class="text-xs text-gray-500">预期收益</div>
                </div>
              </div>

              <!-- 推荐理由 -->
              <div class="bg-gray-50 rounded-lg p-3 mb-3">
                <h5 class="font-medium text-gray-900 mb-2">AI推荐理由</h5>
                <div class="space-y-1 text-xs text-gray-600">
                  <div class="flex items-center">
                    <i class="ri-check-line text-green-500 mr-1"></i>
                    <span>估值优势：PB仅0.8倍，处于历史低位</span>
                  </div>
                  <div class="flex items-center">
                    <i class="ri-check-line text-green-500 mr-1"></i>
                    <span>政策利好：央行降准释放流动性，银行股受益</span>
                  </div>
                  <div class="flex items-center">
                    <i class="ri-check-line text-green-500 mr-1"></i>
                    <span>业绩稳健：ROE保持15%以上，资产质量优良</span>
                  </div>
                </div>
              </div>

              <!-- 操作建议 -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">推荐</span>
                  <span class="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">中长线</span>
                </div>
                <button onclick="addToWatchlist('600036', '招商银行')" class="px-3 py-1 bg-primary text-white text-xs rounded-full hover:bg-blue-600 transition-colors">
                  <i class="ri-star-line mr-1"></i>加自选
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>


    </main>
    <!-- 底部导航栏 -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
      <div class="grid grid-cols-4 h-16">
        <button onclick="window.location.href='index.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-apps-2-line text-lg text-gray-500"></i>
          </div>
          <span class="text-xs text-gray-500">首页</span>
        </button>
        <button onclick="window.location.href='自选股.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-star-line text-lg text-gray-500"></i>
          </div>
          <span class="text-xs text-gray-500">自选股</span>
        </button>
        <button onclick="window.location.href='reports.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-file-list-line text-lg text-gray-500"></i>
          </div>
          <span class="text-xs text-gray-500">报告中心</span>
        </button>
        <button onclick="window.location.href='我的.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-user-line text-lg text-gray-500"></i>
          </div>
          <span class="text-xs text-gray-500">我的</span>
        </button>
      </div>
    </nav>

    <script>
      // 查看股票详情
      function viewStock(code, name) {
        window.location.href = `股票分析.html?code=${code}&name=${encodeURIComponent(name)}`;
      }

      // 返回功能
      function goBack() {
        if (window.history.length > 1) {
          window.history.back();
        } else {
          window.location.href = 'index.html';
        }
      }

      // 打开搜索页面
      function openSearch() {
        window.location.href = 'search.html';
      }

      // 打开消息中心
      function openMessageCenter() {
        window.location.href = '消息中心.html';
      }

      // 分享功能
      function shareApp() {
        if (navigator.share) {
          navigator.share({
            title: '金融智能体 - AI今日推荐',
            text: 'AI智能选股，精准投资',
            url: window.location.href
          }).catch(err => {
            console.log('分享失败:', err);
          });
        } else {
          if (navigator.clipboard) {
            navigator.clipboard.writeText(window.location.href).then(() => {
              alert('链接已复制到剪贴板');
            });
          }
        }
      }

      document.addEventListener("DOMContentLoaded", function () {
        // 股票数据
        const stockData = {
          short: [
            { name: '比亚迪', code: '002594', price: '245.80', change: '+3.25%', action: '买入', target: '280', reason: '新能源汽车销量创新高，月销量突破30万辆。技术面突破关键阻力位245元，MACD金叉向上，RSI指标显示强势。短期目标价280元，建议分批买入。' },
            { name: '宁德时代', code: '300750', price: '198.50', change: '+2.85%', action: '买入', target: '220', reason: '电池技术全球领先，获得特斯拉、宝马等大额订单。三季度业绩超预期，毛利率回升。技术面放量突破，短期有望冲击220元。' },
            { name: '东方财富', code: '300059', price: '15.68', change: '+4.12%', action: '买入', target: '18', reason: '券商板块轮动行情启动，成交量连续3日放大。公司互联网金融业务增长强劲，用户数突破2亿。技术指标全面向好，短期目标18元。' },
            { name: '中国平安', code: '601318', price: '45.20', change: '+1.95%', action: '持有', target: '52', reason: '保险业务逐步回暖，新业务价值增长15%。估值处于历史低位，PEV仅0.6倍。技术面底部企稳，反弹空间较大，短期目标52元。' },
            { name: '招商银行', code: '600036', price: '35.80', change: '+2.15%', action: '买入', target: '40', reason: '银行股补涨行情，资产质量优异，不良率仅0.91%。ROE保持15%以上，分红收益率4.5%。技术面突破下降趋势线，短期目标40元。' }
          ],
          long: [
            { name: '贵州茅台', code: '600519', price: '1678.50', change: '+1.25%', action: '买入', target: '1850', reason: '白酒行业龙头地位稳固，品牌护城河深厚。高端消费复苏，直营渠道改革成效显著。预计未来3年净利润复合增长15%，长期投资价值突出。' },
            { name: '腾讯控股', code: '00700', price: '368.20', change: '+0.85%', action: '买入', target: '420', reason: '互联网巨头，游戏业务回暖，云计算增长强劲。微信生态持续扩张，广告收入稳定增长。估值处于合理区间，长期成长确定性高。' },
            { name: '五粮液', code: '000858', price: '156.80', change: '+1.45%', action: '买入', target: '180', reason: '高端白酒需求旺盛，产品结构持续优化。渠道改革见效，经销商库存健康。盈利能力强，毛利率保持75%以上，长期投资价值显著。' },
            { name: '中国移动', code: '600941', price: '68.50', change: '+0.95%', action: '持有', target: '75', reason: '5G建设龙头，网络覆盖全国领先。现金流稳定，年分红率超过7%。政企业务快速增长，数字化转型成效显著，适合长期持有。' },
            { name: '药明康德', code: '603259', price: '85.60', change: '+2.35%', action: '买入', target: '100', reason: 'CRO行业全球领军企业，客户遍布全球。新药研发需求旺盛，订单饱满。全球化布局完善，长期成长确定性高，目标价100元。' }
          ]
        };

        // 生成股票列表HTML
        function generateStockHTML(stocks, isShortTerm) {
          return stocks.map(stock => {
            let actionColor = '';
            if (stock.action === '买入') {
              actionColor = 'bg-green-100 text-green-700';
            } else if (stock.action === '持有') {
              actionColor = 'bg-blue-100 text-blue-700';
            } else {
              actionColor = 'bg-yellow-100 text-yellow-700';
            }

            return `
              <div class="px-4 py-4 hover:bg-gray-50 cursor-pointer" onclick="viewStock('${stock.code}', '${stock.name}')">
                <div class="flex items-start justify-between mb-3">
                  <div class="flex-1">
                    <div class="flex items-center space-x-2 mb-2">
                      <span class="font-medium text-gray-900">${stock.name}</span>
                      <span class="text-sm text-gray-500">${stock.code}</span>
                    </div>
                    <div class="flex items-center space-x-2 mb-2">
                      <span class="text-xs ${actionColor} px-2 py-1 rounded">${stock.action}</span>
                      <span class="text-xs text-gray-500">目标价: ${stock.target}</span>
                      <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">${isShortTerm ? '短线' : '中长线'}</span>
                    </div>
                    <div class="text-xs text-gray-600 leading-relaxed">
                      <i class="ri-lightbulb-line mr-1 text-yellow-500"></i>
                      ${stock.reason}
                    </div>
                  </div>
                  <div class="text-right ml-4">
                    <div class="font-medium text-gray-900">${stock.price}</div>
                    <div class="text-sm ${stock.change.startsWith('+') ? 'text-red-500' : 'text-green-500'}">${stock.change}</div>
                  </div>
                </div>
              </div>
            `;
          }).join('');
        }

        // 投资周期切换
        const periodButtons = document.querySelectorAll('.investment-period');
        periodButtons.forEach(button => {
          button.addEventListener('click', function() {
            periodButtons.forEach(btn => {
              btn.classList.remove('active', 'bg-primary', 'text-white');
              btn.classList.add('border-gray-200', 'bg-white', 'text-gray-700');
            });
            this.classList.add('active', 'bg-primary', 'text-white');
            this.classList.remove('border-gray-200', 'bg-white', 'text-gray-700');

            // 更新股票列表
            const stockList = document.getElementById('stock-list');
            const isShortTerm = this.dataset.period === 'short';
            const stocks = isShortTerm ? stockData.short : stockData.long;
            stockList.innerHTML = generateStockHTML(stocks, isShortTerm);
          });
        });



        // 股票分析
        function analyzeStock(stock) {
          alert(`正在分析 ${stock.name}(${stock.code})...\n\n当前价格：¥${stock.price}\n涨跌幅：${stock.change}\n推荐理由：${stock.reason}`);
        }

        // 刷新股票
        document.getElementById('refresh-stocks').addEventListener('click', function() {
          this.innerHTML = '<i class="ri-loader-4-line mr-1 animate-spin"></i>刷新中...';
          setTimeout(() => {
            this.innerHTML = '<i class="ri-refresh-line mr-1"></i>刷新';
            generateStockList();
          }, 1500);
        });

        // 查看股票详情
        function viewStock(code, name) {
          window.location.href = `股票分析.html?code=${code}&name=${encodeURIComponent(name)}`;
        }

        // 添加到自选股
        function addToWatchlist(code, name) {
          // 检查是否已经在自选股中
          const watchlist = JSON.parse(localStorage.getItem('watchlist') || '[]');
          const exists = watchlist.some(stock => stock.code === code);

          if (exists) {
            showToast(`${name} 已在自选股中`, 'warning');
            return;
          }

          // 添加到自选股
          watchlist.push({
            code: code,
            name: name,
            addTime: new Date().toISOString()
          });

          localStorage.setItem('watchlist', JSON.stringify(watchlist));
          showToast(`已将 ${name} 添加到自选股`, 'success');
        }

        // 显示提示消息
        function showToast(message, type = 'success') {
          const toast = document.createElement('div');
          toast.className = `fixed top-20 left-1/2 transform -translate-x-1/2 z-50 px-4 py-2 rounded-lg text-white text-sm font-medium transition-all duration-300 ${
            type === 'success' ? 'bg-green-500' : 'bg-yellow-500'
          }`;
          toast.textContent = message;

          document.body.appendChild(toast);

          // 显示动画
          setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translate(-50%, 0)';
          }, 100);

          // 3秒后移除
          setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translate(-50%, -20px)';
            setTimeout(() => {
              document.body.removeChild(toast);
            }, 300);
          }, 3000);
        }

        // 初始化页面
        setTimeout(() => {
          generateStockList();
        }, 100);

        // 底部导航栏交互
        const tabItems = document.querySelectorAll(".tab-item");
        tabItems.forEach((item) => {
          item.addEventListener("click", function () {
            tabItems.forEach((tab) => {
              const icon = tab.querySelector("i");
              const text = tab.querySelector("span");
              icon.classList.remove("text-primary");
              icon.classList.add("text-gray-500");
              text.classList.remove("text-primary", "font-medium");
              text.classList.add("text-gray-500");
              tab.classList.remove("active");
            });
            const icon = this.querySelector("i");
            const text = this.querySelector("span");
            icon.classList.remove("text-gray-500");
            icon.classList.add("text-primary");
            text.classList.remove("text-gray-500");
            text.classList.add("text-primary", "font-medium");
            this.classList.add("active");
          });
        });

        // 返回功能
        window.goBack = function() {
          if (window.history.length > 1) {
            window.history.back();
          } else {
            window.location.href = 'index.html';
          }
        };

        // 分享功能
        window.shareApp = function() {
          if (navigator.share) {
            navigator.share({
              title: '金融智能体 - AI选股',
              text: 'AI智能选股，精准投资',
              url: window.location.href
            }).catch(err => {
              console.log('分享失败:', err);
            });
          } else {
            if (navigator.clipboard) {
              navigator.clipboard.writeText(window.location.href).then(() => {
                alert('链接已复制到剪贴板');
              });
            }
          }
        };
      });

      // 返回功能
      function goBack() {
        if (window.history.length > 1) {
          window.history.back();
        } else {
          window.location.href = 'index.html';
        }
      }

      // 分享功能
      function shareApp() {
        if (navigator.share) {
          navigator.share({
            title: '金融智能体 - AI今日推荐',
            text: 'AI智能选股，精准投资',
            url: window.location.href
          }).catch(err => {
            console.log('分享失败:', err);
          });
        } else {
          if (navigator.clipboard) {
            navigator.clipboard.writeText(window.location.href).then(() => {
              alert('链接已复制到剪贴板');
            });
          }
        }
      }
    </script>
  </body>
</html>