<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>关于 - AI股票分析师</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: "#3B82F6",
            secondary: "#F1F5F9",
          },
        },
      },
    };
  </script>
  <style>
    .gradient-bg { 
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  </style>
</head>
<body class="bg-gray-50 min-h-screen">
  <!-- 顶部导航栏 -->
  <nav class="fixed top-0 w-full bg-white z-50 px-4 py-3 border-b border-gray-100">
    <div class="flex items-center justify-between">
      <div class="font-['Pacifico'] text-xl text-primary">logo</div>
      <div class="flex-1 mx-4">
        <div class="relative">
          <input
            type="text"
            placeholder="搜索功能"
            class="w-full bg-gray-50 rounded-full px-4 py-2 pl-10 text-sm border-none"
          />
          <div
            class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 flex items-center justify-center"
          >
            <i class="ri-search-line text-gray-400 text-sm"></i>
          </div>
        </div>
      </div>
      <div class="flex space-x-2">
        <button
          class="w-8 h-8 flex items-center justify-center cursor-pointer"
        >
          <i class="ri-more-line text-gray-600"></i>
        </button>
        <button
          class="w-8 h-8 flex items-center justify-center cursor-pointer"
        >
          <i class="ri-notification-line text-gray-600"></i>
        </button>
      </div>
    </div>
  </nav>

  <!-- 主要内容区域 -->
  <main class="pt-16 pb-20 px-4">
    <!-- 产品介绍 -->
    <section class="mb-6">
      <div class="gradient-bg rounded-xl p-6 text-white text-center">
        <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
          <i class="ri-robot-line text-3xl"></i>
        </div>
        <h2 class="text-xl font-bold mb-2">AI股票分析师</h2>
        <p class="text-sm opacity-90 mb-4">专业的AI驱动股票分析平台</p>
        <div class="text-xs opacity-80">版本 1.0.0 MVP</div>
      </div>
    </section>

    <!-- 核心功能 -->
    <section class="mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">核心功能</h3>
      <div class="space-y-3">
        <div class="bg-white rounded-lg p-4 flex items-center">
          <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
            <i class="ri-search-line text-blue-600"></i>
          </div>
          <div>
            <h4 class="font-medium text-gray-900">智能股票分析</h4>
            <p class="text-sm text-gray-600">输入股票代码，获取AI专业分析报告</p>
          </div>
        </div>
        
        <div class="bg-white rounded-lg p-4 flex items-center">
          <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
            <i class="ri-news-line text-green-600"></i>
          </div>
          <div>
            <h4 class="font-medium text-gray-900">实时资讯聚合</h4>
            <p class="text-sm text-gray-600">个股相关新闻实时更新，把握市场动态</p>
          </div>
        </div>
        
        <div class="bg-white rounded-lg p-4 flex items-center">
          <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
            <i class="ri-file-text-line text-purple-600"></i>
          </div>
          <div>
            <h4 class="font-medium text-gray-900">AI交易策略</h4>
            <p class="text-sm text-gray-600">日报周报分析，盘前预测和盘后总结</p>
          </div>
        </div>
        
        <div class="bg-white rounded-lg p-4 flex items-center">
          <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
            <i class="ri-bar-chart-line text-orange-600"></i>
          </div>
          <div>
            <h4 class="font-medium text-gray-900">技术面分析</h4>
            <p class="text-sm text-gray-600">MACD、RSI、KDJ等技术指标综合分析</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 技术架构 -->
    <section class="mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">技术架构</h3>
      <div class="bg-white rounded-lg p-4">
        <div class="space-y-3">
          <div class="flex items-center justify-between py-2 border-b border-gray-100">
            <span class="text-sm text-gray-600">AI模型</span>
            <span class="text-sm font-medium text-gray-900">DeepSeek + 本地知识库</span>
          </div>
          <div class="flex items-center justify-between py-2 border-b border-gray-100">
            <span class="text-sm text-gray-600">数据来源</span>
            <span class="text-sm font-medium text-gray-900">实时股票API + 新闻聚合</span>
          </div>
          <div class="flex items-center justify-between py-2 border-b border-gray-100">
            <span class="text-sm text-gray-600">前端技术</span>
            <span class="text-sm font-medium text-gray-900">HTML5 + TailwindCSS</span>
          </div>
          <div class="flex items-center justify-between py-2">
            <span class="text-sm text-gray-600">响应式设计</span>
            <span class="text-sm font-medium text-gray-900">移动端优先</span>
          </div>
        </div>
      </div>
    </section>

    <!-- 使用说明 -->
    <section class="mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">使用说明</h3>
      <div class="bg-white rounded-lg p-4">
        <div class="space-y-4">
          <div class="flex">
            <div class="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">1</div>
            <div>
              <h4 class="font-medium text-gray-900 mb-1">输入股票代码</h4>
              <p class="text-sm text-gray-600">在首页输入框中输入6位股票代码，如：600519</p>
            </div>
          </div>
          
          <div class="flex">
            <div class="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">2</div>
            <div>
              <h4 class="font-medium text-gray-900 mb-1">选择分析类型</h4>
              <p class="text-sm text-gray-600">选择实时资讯、日报分析或周报分析</p>
            </div>
          </div>
          
          <div class="flex">
            <div class="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">3</div>
            <div>
              <h4 class="font-medium text-gray-900 mb-1">查看AI分析</h4>
              <p class="text-sm text-gray-600">获取专业的AI分析报告和投资建议</p>
            </div>
          </div>
          
          <div class="flex">
            <div class="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">4</div>
            <div>
              <h4 class="font-medium text-gray-900 mb-1">历史报告查看</h4>
              <p class="text-sm text-gray-600">在报告中心查看所有历史分析报告</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 免责声明 -->
    <section class="mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">免责声明</h3>
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div class="flex items-start">
          <i class="ri-alert-line text-yellow-600 text-lg mr-2 mt-0.5"></i>
          <div class="text-sm text-gray-700">
            <p class="mb-2">本产品提供的所有分析报告和投资建议仅供参考，不构成投资决策依据。</p>
            <p class="mb-2">股市有风险，投资需谨慎。用户应根据自身情况做出独立的投资决策。</p>
            <p>AI分析结果基于历史数据和算法模型，不保证未来收益。</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 联系信息 -->
    <section>
      <h3 class="text-lg font-semibold text-gray-900 mb-4">联系我们</h3>
      <div class="bg-white rounded-lg p-4">
        <div class="space-y-3">
          <div class="flex items-center">
            <i class="ri-mail-line text-gray-400 mr-3"></i>
            <span class="text-sm text-gray-600"><EMAIL></span>
          </div>
          <div class="flex items-center">
            <i class="ri-github-line text-gray-400 mr-3"></i>
            <span class="text-sm text-gray-600">github.com/ai-stock-analyst</span>
          </div>
          <div class="flex items-center">
            <i class="ri-calendar-line text-gray-400 mr-3"></i>
            <span class="text-sm text-gray-600">最后更新：2025年7月22日</span>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- 底部导航栏 -->
  <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
    <div class="grid grid-cols-4 h-16">
      <button onclick="window.location.href='index.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-apps-2-line text-lg text-gray-500"></i>
        </div>
        <span class="text-xs text-gray-500">首页</span>
      </button>
      <button onclick="window.location.href='自选股.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-star-line text-lg text-gray-500"></i>
        </div>
        <span class="text-xs text-gray-500">自选股</span>
      </button>
      <button onclick="window.location.href='reports.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-file-list-line text-lg text-gray-500"></i>
        </div>
        <span class="text-xs text-gray-500">报告中心</span>
      </button>
      <button onclick="window.location.href='我的.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item active">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-user-line text-lg text-primary"></i>
        </div>
        <span class="text-xs text-primary">我的</span>
      </button>
    </div>
  </nav>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 底部导航栏交互
      const tabItems = document.querySelectorAll('.tab-item');
      tabItems.forEach(item => {
        item.addEventListener('click', function() {
          tabItems.forEach(tab => {
            const icon = tab.querySelector('i');
            const text = tab.querySelector('span');
            icon.classList.remove('text-primary');
            icon.classList.add('text-gray-500');
            text.classList.remove('text-primary');
            text.classList.add('text-gray-500');
          });
          
          const icon = this.querySelector('i');
          const text = this.querySelector('span');
          icon.classList.remove('text-gray-500');
          icon.classList.add('text-primary');
          text.classList.remove('text-gray-500');
          text.classList.add('text-primary');
        });
      });
    });
  </script>
</body>
</html>
