<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>自选股 - 金融智能体</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#3B82F6",
              secondary: "#F1F5F9",
            },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <style>
      .tab-active { color: #2563eb; border-bottom: 2px solid #2563eb; }
      .tab-inactive { color: #333; }
      .stock-row { transition: background 0.2s; }
      .stock-row:hover { background: #f5f7fa; }
      .add-btn { color: #2563eb; }
      .bottom-bar-active { color: #2563eb; }
      .bottom-bar-inactive { color: #888; }
    </style>
  </head>
  <body class="bg-[#f7f8fa] pb-16">
    <!-- 顶部搜索栏 -->
     <nav
      class="fixed top-0 w-full bg-white z-50 px-4 py-3 border-b border-gray-100"
    >
      <div class="flex items-center justify-between">
        <div class="font-['Pacifico'] text-xl text-primary">logo</div>
        <div class="flex-1 mx-4">
          <div class="relative">
            <input
              type="text"
              placeholder="搜索股票/板块"
              class="w-full bg-gray-50 rounded-full px-4 py-2 pl-10 text-sm border-none"
            />
            <div
              class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 flex items-center justify-center"
            >
              <i class="ri-search-line text-gray-400 text-sm"></i>
            </div>
          </div>
        </div>
        <div class="flex space-x-2">
          <button
            class="w-8 h-8 flex items-center justify-center cursor-pointer"
          >
            <i class="ri-more-line text-gray-600"></i>
          </button>
          <button
            class="w-8 h-8 flex items-center justify-center cursor-pointer"
          >
            <i class="ri-notification-line text-gray-600"></i>
          </button>
        </div>
      </div>
    </nav>
    
    <!-- 搜索和分类标签 -->
    <div class="bg-white px-3 pt-20 pb-2 shadow-sm sticky top-0 z-10">
      <div class="flex items-center rounded-full bg-[#f5f7fa] px-3 py-2">
        <i class="ri-search-line text-lg text-gray-400 mr-2"></i>
        <input type="text" placeholder="搜索股票/板块" class="bg-transparent outline-none flex-1 text-sm" />
        <i class="ri-more-2-fill text-lg text-gray-400 ml-2"></i>
        <i class="ri-camera-3-line text-lg text-gray-400 ml-2"></i>
      </div>
      <div class="flex mt-2 space-x-4 text-sm">
        <span class="tab-active cursor-pointer">全部</span>
        <span class="tab-inactive cursor-pointer">沪深</span>
        <span class="tab-inactive cursor-pointer">港股</span>
        <span class="tab-inactive cursor-pointer">美股</span>
        <span class="tab-inactive cursor-pointer">基金</span>
      </div>
    </div>
    
    <!-- 股票列表 -->
    <div class="bg-white mt-2 mx-2 rounded-lg shadow-sm overflow-hidden">
      <div class="flex px-4 py-2 text-xs text-gray-400 border-b">
        <div class="flex-1">股票</div>
        <div class="w-24 text-right">最新价</div>
        <div class="w-20 text-right">涨跌幅</div>
      </div>
      
      <!-- 贵州茅台 -->
      <div class="stock-row flex items-center px-4 py-3 border-b cursor-pointer hover:bg-gray-50">
        <div class="flex-1" onclick="analyzeStock('600519', '贵州茅台')">
          <div class="flex items-center">
            <span class="text-base text-gray-900 font-medium">贵州茅台</span>
            <span class="text-xs text-gray-400 ml-2">600519</span>
          </div>
          <div class="flex items-center space-x-2 mt-1">
            <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">AI建议：持有</span>
          </div>
        </div>
        <div class="w-24 text-right">
          <div class="text-base text-gray-900">1678.50</div>
          <div class="text-xs text-green-600">模拟 +12.5%</div>
        </div>
        <div class="w-20 text-right">
          <span class="bg-red-500 text-white px-2 py-1 rounded text-xs">+2.35%</span>
          <button onclick="showHistoryReports('600519', '贵州茅台')" class="mt-1 text-xs text-primary">
            <i class="ri-file-text-line"></i>
          </button>
        </div>
      </div>
      
      <!-- 比亚迪 -->
      <div class="stock-row flex items-center px-4 py-3 border-b cursor-pointer hover:bg-gray-50">
        <div class="flex-1" onclick="analyzeStock('002594', '比亚迪')">
          <div class="flex items-center">
            <span class="text-base text-gray-900 font-medium">比亚迪</span>
            <span class="text-xs text-gray-400 ml-2">002594</span>
          </div>
          <div class="flex items-center space-x-2 mt-1">
            <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">AI建议：买入</span>
          </div>
        </div>
        <div class="w-24 text-right">
          <div class="text-base text-gray-900">245.80</div>
          <div class="text-xs text-green-600">模拟 +8.2%</div>
        </div>
        <div class="w-20 text-right">
          <span class="bg-red-500 text-white px-2 py-1 rounded text-xs">+3.25%</span>
          <button onclick="showHistoryReports('002594', '比亚迪')" class="mt-1 text-xs text-primary">
            <i class="ri-file-text-line"></i>
          </button>
        </div>
      </div>
      
      <!-- 宁德时代 -->
      <div class="stock-row flex items-center px-4 py-3 border-b cursor-pointer hover:bg-gray-50">
        <div class="flex-1" onclick="analyzeStock('300750', '宁德时代')">
          <div class="flex items-center">
            <span class="text-base text-gray-900 font-medium">宁德时代</span>
            <span class="text-xs text-gray-400 ml-2">300750</span>
          </div>
          <div class="flex items-center space-x-2 mt-1">
            <span class="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded">AI建议：观望</span>
          </div>
        </div>
        <div class="w-24 text-right">
          <div class="text-base text-gray-900">185.20</div>
          <div class="text-xs text-red-600">模拟 -2.1%</div>
        </div>
        <div class="w-20 text-right">
          <span class="bg-green-500 text-white px-2 py-1 rounded text-xs">-0.65%</span>
          <button onclick="showHistoryReports('300750', '宁德时代')" class="mt-1 text-xs text-primary">
            <i class="ri-file-text-line"></i>
          </button>
        </div>
      </div>
      
      <!-- 中国平安 -->
      <div class="stock-row flex items-center px-4 py-3 border-b cursor-pointer hover:bg-gray-50">
        <div class="flex-1" onclick="analyzeStock('601318', '中国平安')">
          <div class="flex items-center">
            <span class="text-base text-gray-900 font-medium">中国平安</span>
            <span class="text-xs text-gray-400 ml-2">601318</span>
          </div>
          <div class="flex items-center space-x-2 mt-1">
            <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">AI建议：买入</span>
          </div>
        </div>
        <div class="w-24 text-right">
          <div class="text-base text-gray-900">42.50</div>
          <div class="text-xs text-green-600">模拟 +5.6%</div>
        </div>
        <div class="w-20 text-right">
          <span class="bg-red-500 text-white px-2 py-1 rounded text-xs">+1.85%</span>
          <button onclick="showHistoryReports('601318', '中国平安')" class="mt-1 text-xs text-primary">
            <i class="ri-file-text-line"></i>
          </button>
        </div>
      </div>
      
      <!-- 添加自选股按钮 -->
      <div class="flex items-center justify-center py-4 border-t">
        <button onclick="addStock()" class="flex items-center text-primary text-sm">
          <i class="ri-add-line mr-1"></i>
          添加自选股
        </button>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
      <div class="grid grid-cols-4 h-16">
        <button onclick="window.location.href='index.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-apps-2-line text-lg text-gray-500"></i>
          </div>
          <span class="text-xs text-gray-500">首页</span>
        </button>
        <button onclick="window.location.href='watchlist.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item active">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-star-line text-lg text-primary"></i>
          </div>
          <span class="text-xs text-primary">自选股</span>
        </button>
        <button onclick="window.location.href='reports.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-file-list-line text-lg text-gray-500"></i>
          </div>
          <span class="text-xs text-gray-500">报告中心</span>
        </button>
        <button onclick="window.location.href='about.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-user-line text-lg text-gray-500"></i>
          </div>
          <span class="text-xs text-gray-500">我的</span>
        </button>
      </div>
    </nav>

    <script>
      // 分析股票
      function analyzeStock(stockCode, stockName) {
        // 跳转到首页并自动分析该股票
        window.location.href = `index.html?code=${stockCode}&name=${stockName}`;
      }

      // 显示历史报告
      function showHistoryReports(stockCode, stockName) {
        alert(`查看${stockName}(${stockCode})的历史分析报告`);
        // 这里可以跳转到报告详情页面
      }

      // 添加自选股
      function addStock() {
        const stockCode = prompt('请输入股票代码（如：600519）：');
        if (stockCode) {
          alert(`已添加股票：${stockCode}`);
          // 这里可以实现添加股票的逻辑
        }
      }

      // 标签页切换
      document.addEventListener('DOMContentLoaded', function() {
        const tabs = document.querySelectorAll('.tab-active, .tab-inactive');
        tabs.forEach(tab => {
          tab.addEventListener('click', function() {
            tabs.forEach(t => {
              t.classList.remove('tab-active');
              t.classList.add('tab-inactive');
            });
            this.classList.remove('tab-inactive');
            this.classList.add('tab-active');
          });
        });

        // 底部导航栏交互
        const tabItems = document.querySelectorAll('.tab-item');
        tabItems.forEach(item => {
          item.addEventListener('click', function() {
            tabItems.forEach(tab => {
              const icon = tab.querySelector('i');
              const text = tab.querySelector('span');
              icon.classList.remove('text-primary');
              icon.classList.add('text-gray-500');
              text.classList.remove('text-primary');
              text.classList.add('text-gray-500');
            });
            
            const icon = this.querySelector('i');
            const text = this.querySelector('span');
            icon.classList.remove('text-gray-500');
            icon.classList.add('text-primary');
            text.classList.remove('text-gray-500');
            text.classList.add('text-primary');
          });
        });
      });
    </script>
  </body>
</html>
