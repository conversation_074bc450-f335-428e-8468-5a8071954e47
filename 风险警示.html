<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>风险警示 - 金融智能体</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: "#3B82F6",
            secondary: "#F1F5F9",
            danger: "#EF4444",
            warning: "#F59E0B",
          },
        },
      },
    };
  </script>
  <style>
    .risk-high { 
      background: linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%);
      border-color: #EF4444;
    }
    .risk-medium { 
      background: linear-gradient(135deg, #FEF3C7 0%, #FDE68A 100%);
      border-color: #F59E0B;
    }
    .risk-low { 
      background: linear-gradient(135deg, #D1FAE5 0%, #A7F3D0 100%);
      border-color: #10B981;
    }
    .pulse-warning {
      animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }
  </style>
</head>
<body class="bg-gray-50 min-h-screen">
  <!-- 顶部导航栏 -->
  <nav class="fixed top-0 w-full bg-white z-50 px-4 py-3 border-b border-gray-100">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <button onclick="goBack()" class="w-8 h-8 flex items-center justify-center mr-3 cursor-pointer">
          <i class="ri-arrow-left-line text-gray-600 text-lg"></i>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">风险警示</h1>
      </div>
      <div class="flex items-center space-x-2">
        <!-- 微信小程序分享按钮 -->
        <button onclick="shareApp()" class="w-8 h-8 flex items-center justify-center">
          <div class="flex space-x-0.5">
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
          </div>
        </button>
        <!-- 微信小程序返回主界面按钮 -->
        <button class="w-8 h-8 flex items-center justify-center">
          <div class="w-5 h-5 border-2 border-gray-600 rounded-full flex items-center justify-center">
            <div class="w-2 h-2 bg-gray-600 rounded-full"></div>
          </div>
        </button>
      </div>
    </div>
  </nav>

  <!-- 主要内容区域 -->
  <main class="pt-20 pb-20 px-4">
    <!-- 风险等级总览 -->
    <section class="mb-6">
      <div class="bg-gradient-to-r from-red-500 to-orange-500 rounded-xl p-6 text-white">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <i class="ri-alert-line text-2xl mr-3 pulse-warning"></i>
            <div>
              <h2 class="text-lg font-semibold">当前风险等级</h2>
              <p class="text-sm opacity-90">基于市场环境和持仓分析</p>
            </div>
          </div>
          <div class="text-right">
            <div class="text-2xl font-bold">中等</div>
            <div class="text-sm opacity-90">风险指数: 6.5/10</div>
          </div>
        </div>
        <div class="grid grid-cols-3 gap-4 text-center text-sm">
          <div>
            <div class="font-bold">3只</div>
            <div class="opacity-90">高风险股票</div>
          </div>
          <div>
            <div class="font-bold">2只</div>
            <div class="opacity-90">中风险股票</div>
          </div>
          <div>
            <div class="font-bold">3只</div>
            <div class="opacity-90">低风险股票</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 重要风险提醒 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm border-l-4 border-red-500">
        <div class="flex items-center mb-3">
          <i class="ri-error-warning-line text-red-500 text-xl mr-2"></i>
          <h3 class="font-semibold text-gray-900">重要风险提醒</h3>
        </div>
        <div class="space-y-2 text-sm text-gray-700">
          <div class="flex items-start">
            <i class="ri-arrow-right-s-line text-red-500 mt-0.5 mr-1"></i>
            <span>股市有风险，投资需谨慎。AI分析仅供参考，不构成投资建议。</span>
          </div>
          <div class="flex items-start">
            <i class="ri-arrow-right-s-line text-red-500 mt-0.5 mr-1"></i>
            <span>请根据自身风险承受能力理性投资，切勿盲目跟风。</span>
          </div>
          <div class="flex items-start">
            <i class="ri-arrow-right-s-line text-red-500 mt-0.5 mr-1"></i>
            <span>过往业绩不代表未来表现，投资有可能面临本金损失。</span>
          </div>
        </div>
      </div>
    </section>

    <!-- 自选股风险分析 -->
    <section class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">自选股风险分析</h3>
        <button onclick="refreshRiskAnalysis()" class="text-sm text-primary flex items-center">
          <i class="ri-refresh-line mr-1"></i>刷新
        </button>
      </div>

      <div class="space-y-3">
        <!-- 高风险股票 -->
        <div class="risk-high border-2 rounded-xl p-4">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
              <i class="ri-alert-fill text-red-600 text-lg mr-2"></i>
              <span class="font-medium text-red-900">高风险股票</span>
            </div>
            <span class="text-xs bg-red-600 text-white px-2 py-1 rounded-full">风险等级: 高</span>
          </div>
          
          <div class="space-y-3">
            <div class="bg-white rounded-lg p-3 border border-red-200">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-3">
                  <span class="font-medium text-gray-900">宁德时代</span>
                  <span class="text-sm text-gray-500">300750</span>
                </div>
                <div class="text-right">
                  <div class="text-sm font-medium text-red-600">-2.1%</div>
                  <div class="text-xs text-gray-500">持仓亏损</div>
                </div>
              </div>
              <div class="text-xs text-red-800 space-y-1">
                <div>• 技术面：跌破重要支撑位，短期趋势偏弱</div>
                <div>• 基本面：行业竞争加剧，盈利能力下降</div>
                <div>• 建议：考虑减仓或设置止损位</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 中风险股票 -->
        <div class="risk-medium border-2 rounded-xl p-4">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
              <i class="ri-error-warning-line text-yellow-600 text-lg mr-2"></i>
              <span class="font-medium text-yellow-900">中风险股票</span>
            </div>
            <span class="text-xs bg-yellow-600 text-white px-2 py-1 rounded-full">风险等级: 中</span>
          </div>
          
          <div class="space-y-3">
            <div class="bg-white rounded-lg p-3 border border-yellow-200">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-3">
                  <span class="font-medium text-gray-900">比亚迪</span>
                  <span class="text-sm text-gray-500">002594</span>
                </div>
                <div class="text-right">
                  <div class="text-sm font-medium text-green-600">+8.2%</div>
                  <div class="text-xs text-gray-500">持仓盈利</div>
                </div>
              </div>
              <div class="text-xs text-yellow-800 space-y-1">
                <div>• 技术面：短期涨幅较大，存在回调风险</div>
                <div>• 基本面：业绩增长稳定，但估值偏高</div>
                <div>• 建议：适当控制仓位，关注回调机会</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 低风险股票 -->
        <div class="risk-low border-2 rounded-xl p-4">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
              <i class="ri-shield-check-line text-green-600 text-lg mr-2"></i>
              <span class="font-medium text-green-900">低风险股票</span>
            </div>
            <span class="text-xs bg-green-600 text-white px-2 py-1 rounded-full">风险等级: 低</span>
          </div>
          
          <div class="space-y-3">
            <div class="bg-white rounded-lg p-3 border border-green-200">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-3">
                  <span class="font-medium text-gray-900">贵州茅台</span>
                  <span class="text-sm text-gray-500">600519</span>
                </div>
                <div class="text-right">
                  <div class="text-sm font-medium text-green-600">+12.5%</div>
                  <div class="text-xs text-gray-500">持仓盈利</div>
                </div>
              </div>
              <div class="text-xs text-green-800 space-y-1">
                <div>• 技术面：趋势稳健，支撑位明确</div>
                <div>• 基本面：行业龙头地位稳固，盈利稳定</div>
                <div>• 建议：可继续持有，适合长期投资</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 市场风险指标 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <h3 class="font-semibold text-gray-900 mb-4">市场风险指标</h3>
        
        <div class="grid grid-cols-2 gap-4 mb-4">
          <div class="text-center p-3 bg-red-50 rounded-lg">
            <div class="text-lg font-bold text-red-600">68</div>
            <div class="text-xs text-gray-600">恐慌指数</div>
            <div class="text-xs text-red-600 mt-1">市场恐慌</div>
          </div>
          <div class="text-center p-3 bg-yellow-50 rounded-lg">
            <div class="text-lg font-bold text-yellow-600">7.2%</div>
            <div class="text-xs text-gray-600">波动率</div>
            <div class="text-xs text-yellow-600 mt-1">波动较大</div>
          </div>
        </div>

        <div class="space-y-2 text-sm">
          <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
            <span class="text-gray-700">市场情绪</span>
            <span class="text-red-600 font-medium">偏悲观</span>
          </div>
          <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
            <span class="text-gray-700">资金流向</span>
            <span class="text-red-600 font-medium">净流出</span>
          </div>
          <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
            <span class="text-gray-700">政策环境</span>
            <span class="text-green-600 font-medium">相对稳定</span>
          </div>
        </div>
      </div>
    </section>

    <!-- 风险控制建议 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <h3 class="font-semibold text-gray-900 mb-4">风险控制建议</h3>
        
        <div class="space-y-3">
          <div class="flex items-start p-3 bg-blue-50 rounded-lg">
            <i class="ri-lightbulb-line text-blue-500 text-lg mr-3 mt-0.5"></i>
            <div>
              <div class="font-medium text-blue-900 mb-1">仓位控制</div>
              <div class="text-sm text-blue-800">建议将总仓位控制在60%以下，保留充足现金应对市场波动。</div>
            </div>
          </div>
          
          <div class="flex items-start p-3 bg-green-50 rounded-lg">
            <i class="ri-pie-chart-line text-green-500 text-lg mr-3 mt-0.5"></i>
            <div>
              <div class="font-medium text-green-900 mb-1">分散投资</div>
              <div class="text-sm text-green-800">避免单一股票仓位过重，建议单只股票仓位不超过总资产的20%。</div>
            </div>
          </div>
          
          <div class="flex items-start p-3 bg-orange-50 rounded-lg">
            <i class="ri-shield-line text-orange-500 text-lg mr-3 mt-0.5"></i>
            <div>
              <div class="font-medium text-orange-900 mb-1">止损设置</div>
              <div class="text-sm text-orange-800">为高风险股票设置止损位，建议止损幅度为10-15%。</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- 底部导航栏 -->
  <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
    <div class="grid grid-cols-4 h-16">
      <button onclick="window.location.href='index.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-apps-2-line text-lg text-gray-500"></i>
        </div>
        <span class="text-xs text-gray-500">首页</span>
      </button>
      <button onclick="window.location.href='自选股.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-star-line text-lg text-gray-500"></i>
        </div>
        <span class="text-xs text-gray-500">自选股</span>
      </button>
      <button onclick="window.location.href='reports.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-file-list-line text-lg text-gray-500"></i>
        </div>
        <span class="text-xs text-gray-500">报告中心</span>
      </button>
      <button onclick="window.location.href='我的.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-user-line text-lg text-gray-500"></i>
        </div>
        <span class="text-xs text-gray-500">我的</span>
      </button>
    </div>
  </nav>

  <script>
    document.addEventListener("DOMContentLoaded", function () {
      // 刷新风险分析
      window.refreshRiskAnalysis = function() {
        const button = event.target.closest('button');
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="ri-loader-4-line mr-1 animate-spin"></i>刷新中...';
        
        setTimeout(() => {
          button.innerHTML = originalHTML;
          // 这里可以添加实际的数据刷新逻辑
          console.log('风险分析已刷新');
        }, 2000);
      };

      // 底部导航栏交互
      const tabItems = document.querySelectorAll(".tab-item");
      tabItems.forEach((item) => {
        item.addEventListener("click", function () {
          tabItems.forEach((tab) => {
            const icon = tab.querySelector("i");
            const text = tab.querySelector("span");
            icon.classList.remove("text-primary");
            icon.classList.add("text-gray-500");
            text.classList.remove("text-primary", "font-medium");
            text.classList.add("text-gray-500");
            tab.classList.remove("active");
          });
          const icon = this.querySelector("i");
          const text = this.querySelector("span");
          icon.classList.remove("text-gray-500");
          icon.classList.add("text-primary");
          text.classList.remove("text-gray-500");
          text.classList.add("text-primary", "font-medium");
          this.classList.add("active");
        });
      });

      // 返回功能
      window.goBack = function() {
        if (window.history.length > 1) {
          window.history.back();
        } else {
          window.location.href = 'index.html';
        }
      };

      // 分享功能
      window.shareApp = function() {
        if (navigator.share) {
          navigator.share({
            title: '金融智能体 - 风险警示',
            text: '查看投资风险提醒',
            url: window.location.href
          }).catch(err => {
            console.log('分享失败:', err);
          });
        } else {
          if (navigator.clipboard) {
            navigator.clipboard.writeText(window.location.href).then(() => {
              alert('链接已复制到剪贴板');
            });
          }
        }
      };
    });
  </script>
</body>
</html>
