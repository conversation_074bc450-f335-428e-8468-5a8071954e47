<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>金融智能体 - 我的</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#3B82F6",
              secondary: "#F1F5F9",
              danger: "#EF4444",
            },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      .scrollbar-hide { -ms-overflow-style: none; scrollbar-width: none; }
      .scrollbar-hide::-webkit-scrollbar { display: none; }
    </style>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <nav class="fixed top-0 w-full bg-white z-50 px-4 py-3 border-b border-gray-100">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <button onclick="goBack()" class="w-8 h-8 flex items-center justify-center mr-3 cursor-pointer">
            <i class="ri-arrow-left-line text-gray-600 text-lg"></i>
          </button>
          <h1 class="text-lg font-semibold text-gray-900">我的</h1>
        </div>
        <div class="flex items-center space-x-2">
          <!-- 智能体分析按钮 -->
          <button onclick="window.location.href='智能体分析.html'" class="w-8 h-8 flex items-center justify-center">
            <i class="ri-robot-line text-gray-600 text-lg"></i>
          </button>
          <!-- 微信小程序分享按钮 -->
          <button onclick="shareApp()" class="w-8 h-8 flex items-center justify-center">
            <div class="flex space-x-0.5">
              <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
              <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
              <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            </div>
          </button>
          <!-- 微信小程序返回主界面按钮 -->
          <button class="w-8 h-8 flex items-center justify-center">
            <div class="w-5 h-5 border-2 border-gray-600 rounded-full flex items-center justify-center">
              <div class="w-2 h-2 bg-gray-600 rounded-full"></div>
            </div>
          </button>
        </div>
      </div>
    </nav>
    <main class="pt-16 pb-20 px-4">


      <!-- 会员状态卡片 -->
      <div class="mt-4 mb-4">
        <div class="bg-gradient-to-r from-purple-500 to-blue-600 rounded-xl p-4 text-white">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
              <i class="ri-vip-crown-line text-xl mr-2"></i>
              <span class="font-semibold">普通用户</span>
            </div>
            <button onclick="window.location.href='会员升级.html'" id="upgrade-btn" class="bg-white text-purple-600 px-4 py-1 rounded-full text-sm font-medium">
              升级会员
            </button>
          </div>
          <div class="grid grid-cols-3 gap-4 text-center text-sm">
            <div>
              <div class="font-bold">3</div>
              <div class="opacity-90">关注股票</div>
            </div>
            <div>
              <div class="font-bold">每3天</div>
              <div class="opacity-90">报告频率</div>
            </div>
            <div>
              <div class="font-bold">基础</div>
              <div class="opacity-90">AI引擎</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 模拟账户卡片 -->
      <div class="mb-4">
        <div class="bg-white rounded-xl p-4 shadow-sm">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">模拟账户</h3>
            <a href="模拟盘.html" class="text-sm text-primary">查看详情</a>
          </div>

          <div class="grid grid-cols-3 gap-4 mb-4">
            <div class="text-center">
              <div class="text-lg font-bold text-gray-900">¥108,500</div>
              <div class="text-xs text-gray-500">总资产</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-bold text-green-600">+8.5%</div>
              <div class="text-xs text-gray-500">总收益率</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-bold text-green-600">+¥8,500</div>
              <div class="text-xs text-gray-500">总盈亏</div>
            </div>
          </div>

          <div class="flex items-center justify-between text-sm bg-blue-50 rounded-lg p-3">
            <div class="flex items-center">
              <i class="ri-robot-line text-blue-600 mr-2"></i>
              <span class="text-gray-700">AI自动交易</span>
            </div>
            <div class="flex items-center">
              <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              <span class="text-green-600 font-medium">运行中</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 菜单卡片 -->
      <div class="bg-white rounded-xl shadow-sm p-2 divide-y divide-gray-100">
        <button onclick="window.location.href='消息中心.html'" class="flex items-center w-full px-3 py-3 text-gray-800 hover:bg-gray-50">
          <i class="ri-message-3-line text-lg mr-3 text-primary"></i>
          <div class="flex-1 text-left">
            <span>消息中心</span>
            <div class="w-2 h-2 bg-red-500 rounded-full inline-block ml-2"></div>
          </div>
        </button>
        <button onclick="window.location.href='关于我们.html'" class="flex items-center w-full px-3 py-3 text-gray-800 hover:bg-gray-50">
          <i class="ri-user-3-line text-lg mr-3 text-primary"></i>
          <span class="flex-1 text-left">关于我们</span>
        </button>
        <button onclick="window.location.href='免责声明.html'" class="flex items-center w-full px-3 py-3 text-gray-800 hover:bg-gray-50">
          <i class="ri-shield-check-line text-lg mr-3 text-primary"></i>
          <span class="flex-1 text-left">免责声明</span>
        </button>
        <button onclick="window.location.href='隐私政策.html'" class="flex items-center w-full px-3 py-3 text-gray-800 hover:bg-gray-50">
          <i class="ri-lock-2-line text-lg mr-3 text-primary"></i>
          <span class="flex-1 text-left">隐私政策</span>
        </button>
        <button onclick="window.location.href='设置.html'" class="flex items-center w-full px-3 py-3 text-gray-800 hover:bg-gray-50">
          <i class="ri-feedback-line text-lg mr-3 text-primary"></i>
          <span class="flex-1 text-left">设置</span>
        </button>
      </div>
      <!-- 版本号 -->

    </main>
    <!-- 底部导航栏 -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
      <div class="grid grid-cols-4 h-16">
        <button onclick="window.location.href='index.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-apps-2-line text-lg text-gray-500"></i>
          </div>
          <span class="text-xs text-gray-500">首页</span>
        </button>
        <button onclick="window.location.href='自选股.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-star-line text-lg text-gray-500"></i>
          </div>
          <span class="text-xs text-gray-500">自选股</span>
        </button>
        <button onclick="window.location.href='reports.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-file-list-line text-lg text-gray-500"></i>
          </div>
          <span class="text-xs text-gray-500">报告中心</span>
        </button>
        <button onclick="window.location.href='我的.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item active">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-user-line text-lg text-primary"></i>
          </div>
          <span class="text-xs text-primary">我的</span>
        </button>
      </div>
    </nav>
    <script id="bottom-navigation">
      document.addEventListener("DOMContentLoaded", function () {
        const tabItems = document.querySelectorAll(".tab-item");
        tabItems.forEach((item) => {
          item.addEventListener("click", function () {
            tabItems.forEach((tab) => {
              const icon = tab.querySelector("i");
              const text = tab.querySelector("span");
              icon.classList.remove("text-primary");
              icon.classList.add("text-gray-500");
              text.classList.remove("text-primary", "font-medium");
              text.classList.add("text-gray-500");
              tab.classList.remove("active");
            });
            const icon = this.querySelector("i");
            const text = this.querySelector("span");
            icon.classList.remove("text-gray-500");
            icon.classList.add("text-primary");
            text.classList.remove("text-gray-500");
            text.classList.add("text-primary", "font-medium");
            this.classList.add("active");
          });
        });
      });

      // 显示模拟盘功能提示
      function showSimulationModal() {
        alert('模拟盘功能正在开发中，敬请期待！\n\n您可以在股票分析页面查看模拟交易记录。');
      }

      // 返回功能
      function goBack() {
        if (window.history.length > 1) {
          window.history.back();
        } else {
          window.location.href = 'index.html';
        }
      }

      // 分享功能
      function shareApp() {
        if (navigator.share) {
          navigator.share({
            title: '金融智能体 - 个人中心',
            text: '智能投资，专业分析',
            url: window.location.href
          }).catch(err => {
            console.log('分享失败:', err);
          });
        } else {
          if (navigator.clipboard) {
            navigator.clipboard.writeText(window.location.href).then(() => {
              alert('链接已复制到剪贴板');
            });
          }
        }
      }
    </script>
  </body>
</html>