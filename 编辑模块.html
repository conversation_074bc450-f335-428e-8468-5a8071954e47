<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑模块 - 金融智能体</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
      :root {
        --primary-color: #3b82f6;
        --primary-dark: #2563eb;
        --primary-light: #dbeafe;
      }

      .primary { color: var(--primary-color); }
      .bg-primary { background-color: var(--primary-color); }
      .border-primary { border-color: var(--primary-color); }
      .text-primary { color: var(--primary-color); }

      .module-card {
        transition: all 0.3s ease;
        cursor: grab;
      }

      .module-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      }

      .module-card.selected {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      .tab-btn {
        color: #6b7280;
        background: transparent;
      }

      .tab-btn.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
      }

      .tab-panel {
        animation: fadeIn 0.3s ease-in-out;
      }

      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
      }

      .theme-option {
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        border-radius: 12px;
        padding: 4px;
      }

      .theme-option.active {
        border-color: #667eea;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
      }

      .toggle-switch {
        position: relative;
        width: 44px;
        height: 24px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .toggle-switch.active {
        background: rgba(59, 130, 246, 0.8);
      }

      .toggle-switch::before {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 20px;
        height: 20px;
        background: white;
        border-radius: 50%;
        transition: all 0.3s ease;
      }

      .toggle-switch.active::before {
        transform: translateX(20px);
      }

      .add-btn {
        width: 20px;
        height: 20px;
        background: #fbbf24;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 14px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .add-btn:hover {
        background: #f59e0b;
        transform: scale(1.1);
      }

      .remove-btn {
        width: 20px;
        height: 20px;
        background: #ef4444;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 14px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .remove-btn:hover {
        background: #dc2626;
        transform: scale(1.1);
      }
    </style>
  </head>
  <body class="bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white/80 backdrop-blur-md shadow-sm sticky top-0 z-40 border-b border-white/20">
      <div class="flex items-center justify-between px-4 py-3">
        <div class="flex items-center space-x-3">
          <button onclick="goBack()" class="p-2 hover:bg-purple-100 rounded-xl transition-colors">
            <i class="ri-arrow-left-line text-xl text-purple-600"></i>
          </button>
          <div>
            <h1 class="text-lg font-bold text-gray-900">个性化定制</h1>
            <p class="text-xs text-gray-500">打造专属投资界面</p>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <button onclick="previewChanges()" class="px-3 py-1.5 bg-purple-100 text-purple-600 rounded-lg text-sm font-medium hover:bg-purple-200 transition-colors">
            <i class="ri-eye-line mr-1"></i>预览
          </button>
          <button onclick="shareApp()" class="p-2 hover:bg-purple-100 rounded-xl transition-colors">
            <i class="ri-share-line text-xl text-purple-600"></i>
          </button>
        </div>
      </div>
    </header>

    <main class="px-4 py-6 pb-20">
      <!-- 功能选项卡 -->
      <div class="bg-white/60 backdrop-blur-md rounded-2xl p-1 mb-6 border border-white/20">
        <div class="grid grid-cols-3 gap-1">
          <button onclick="switchTab('layout')" class="tab-btn active px-4 py-3 rounded-xl text-sm font-medium transition-all" data-tab="layout">
            <i class="ri-layout-grid-line text-lg mb-1"></i>
            <div>布局设置</div>
          </button>
          <button onclick="switchTab('theme')" class="tab-btn px-4 py-3 rounded-xl text-sm font-medium transition-all" data-tab="theme">
            <i class="ri-palette-line text-lg mb-1"></i>
            <div>主题配色</div>
          </button>
          <button onclick="switchTab('function')" class="tab-btn px-4 py-3 rounded-xl text-sm font-medium transition-all" data-tab="function">
            <i class="ri-settings-3-line text-lg mb-1"></i>
            <div>功能开关</div>
          </button>
        </div>
      </div>

      <!-- 布局设置面板 -->
      <div id="layout-panel" class="tab-panel">
        <!-- 拖动排序提示 -->
        <div class="bg-white/60 backdrop-blur-md rounded-2xl p-4 mb-6 border border-white/20">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                <i class="ri-drag-move-line text-white"></i>
              </div>
              <div>
                <span class="text-lg font-semibold text-gray-900">拖动排序</span>
                <p class="text-sm text-gray-500">长按模块可拖动调整顺序</p>
              </div>
            </div>
            <div class="toggle-switch active" onclick="toggleDragMode(this)"></div>
          </div>
        </div>

        <!-- 已选中的模块 -->
        <section class="mb-8">
          <div class="flex items-center space-x-2 mb-4">
            <div class="w-6 h-6 bg-green-500 rounded-lg flex items-center justify-center">
              <i class="ri-check-line text-white text-sm"></i>
            </div>
            <h2 class="text-lg font-semibold text-gray-900">已启用模块</h2>
            <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">6个</span>
          </div>
          <div class="grid grid-cols-2 gap-3" id="selected-modules">
            <!-- 今日投资思考 -->
            <div class="module-card bg-white/80 backdrop-blur-md rounded-xl p-4 selected border border-purple-200 shadow-lg" data-module="今日投资思考">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-2">
                  <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                    <i class="ri-lightbulb-line text-white text-sm"></i>
                  </div>
                  <span class="text-sm font-semibold text-gray-900">今日投资思考</span>
                </div>
                <div class="remove-btn" onclick="removeModule(this)">-</div>
              </div>
              <p class="text-xs text-gray-500">每日投资话题讨论</p>
            </div>

            <!-- AI今日推荐 -->
            <div class="module-card bg-white/80 backdrop-blur-md rounded-xl p-4 selected border border-blue-200 shadow-lg" data-module="AI今日推荐">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-2">
                  <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                    <i class="ri-robot-line text-white text-sm"></i>
                  </div>
                  <span class="text-sm font-semibold text-gray-900">AI今日推荐</span>
                </div>
                <div class="remove-btn" onclick="removeModule(this)">-</div>
              </div>
              <p class="text-xs text-gray-500">智能推荐股票</p>
            </div>

            <!-- 自选股 -->
            <div class="module-card bg-white/80 backdrop-blur-md rounded-xl p-4 selected border border-yellow-200 shadow-lg" data-module="自选股">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-2">
                  <div class="w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center">
                    <i class="ri-star-line text-white text-sm"></i>
                  </div>
                  <span class="text-sm font-semibold text-gray-900">自选股</span>
                </div>
                <div class="remove-btn" onclick="removeModule(this)">-</div>
              </div>
              <p class="text-xs text-gray-500">我的关注股票</p>
            </div>

            <!-- 最新资讯 -->
            <div class="module-card bg-white/80 backdrop-blur-md rounded-xl p-4 selected border border-green-200 shadow-lg" data-module="最新资讯">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-2">
                  <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                    <i class="ri-news-line text-white text-sm"></i>
                  </div>
                  <span class="text-sm font-semibold text-gray-900">最新资讯</span>
                </div>
                <div class="remove-btn" onclick="removeModule(this)">-</div>
              </div>
              <p class="text-xs text-gray-500">财经新闻动态</p>
            </div>

            <!-- 模拟盘 -->
            <div class="module-card bg-white/80 backdrop-blur-md rounded-xl p-4 selected border border-indigo-200 shadow-lg" data-module="模拟盘">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-2">
                  <div class="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                    <i class="ri-line-chart-line text-white text-sm"></i>
                  </div>
                  <span class="text-sm font-semibold text-gray-900">模拟盘</span>
                </div>
                <div class="remove-btn" onclick="removeModule(this)">-</div>
              </div>
              <p class="text-xs text-gray-500">虚拟投资练习</p>
            </div>

            <!-- 个性化定制 -->
            <div class="module-card bg-white/80 backdrop-blur-md rounded-xl p-4 selected border border-pink-200 shadow-lg" data-module="个性化定制">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-2">
                  <div class="w-8 h-8 bg-gradient-to-r from-pink-500 to-rose-500 rounded-lg flex items-center justify-center">
                    <i class="ri-palette-line text-white text-sm"></i>
                  </div>
                  <span class="text-sm font-semibold text-gray-900">个性化定制</span>
                </div>
                <div class="remove-btn" onclick="removeModule(this)">-</div>
              </div>
              <p class="text-xs text-gray-500">界面个性化设置</p>
            </div>
          </div>
        </section>

        <!-- 已隐藏的模块 -->
        <section class="mb-8">
          <div class="flex items-center space-x-2 mb-4">
            <div class="w-6 h-6 bg-gray-400 rounded-lg flex items-center justify-center">
              <i class="ri-eye-off-line text-white text-sm"></i>
            </div>
            <h2 class="text-lg font-semibold text-gray-900">可添加模块</h2>
            <span class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full">4个</span>
          </div>
          <div class="grid grid-cols-2 gap-3" id="hidden-modules">
            <!-- 股票分析 -->
            <div class="module-card bg-white/60 backdrop-blur-md rounded-xl p-4 border border-gray-200" data-module="股票分析">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-2">
                  <div class="w-8 h-8 bg-gray-400 rounded-lg flex items-center justify-center">
                    <i class="ri-bar-chart-line text-white text-sm"></i>
                  </div>
                  <span class="text-sm font-semibold text-gray-900">股票分析</span>
                </div>
                <div class="add-btn" onclick="addModule(this)">+</div>
              </div>
              <p class="text-xs text-gray-500">深度股票分析</p>
            </div>

            <!-- 投资组合 -->
            <div class="module-card bg-white/60 backdrop-blur-md rounded-xl p-4 border border-gray-200" data-module="投资组合">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-2">
                  <div class="w-8 h-8 bg-gray-400 rounded-lg flex items-center justify-center">
                    <i class="ri-pie-chart-line text-white text-sm"></i>
                  </div>
                  <span class="text-sm font-semibold text-gray-900">投资组合</span>
                </div>
                <div class="add-btn" onclick="addModule(this)">+</div>
              </div>
              <p class="text-xs text-gray-500">资产配置管理</p>
            </div>

            <!-- 风险评估 -->
            <div class="module-card bg-white/60 backdrop-blur-md rounded-xl p-4 border border-gray-200" data-module="风险评估">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-2">
                  <div class="w-8 h-8 bg-gray-400 rounded-lg flex items-center justify-center">
                    <i class="ri-shield-check-line text-white text-sm"></i>
                  </div>
                  <span class="text-sm font-semibold text-gray-900">风险评估</span>
                </div>
                <div class="add-btn" onclick="addModule(this)">+</div>
              </div>
              <p class="text-xs text-gray-500">投资风险分析</p>
            </div>

            <!-- 技术指标 -->
            <div class="module-card bg-white/60 backdrop-blur-md rounded-xl p-4 border border-gray-200" data-module="技术指标">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-2">
                  <div class="w-8 h-8 bg-gray-400 rounded-lg flex items-center justify-center">
                    <i class="ri-line-chart-fill text-white text-sm"></i>
                  </div>
                  <span class="text-sm font-semibold text-gray-900">技术指标</span>
                </div>
                <div class="add-btn" onclick="addModule(this)">+</div>
              </div>
              <p class="text-xs text-gray-500">技术分析工具</p>
            </div>
          </div>
        </section>
      </div>

      <!-- 主题配色面板 -->
      <div id="theme-panel" class="tab-panel hidden">
        <div class="bg-white/60 backdrop-blur-md rounded-2xl p-6 mb-6 border border-white/20">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">选择主题配色</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="theme-option active" data-theme="default" onclick="selectTheme('default')">
              <div class="bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl p-4 text-white mb-2">
                <div class="text-sm font-medium">默认主题</div>
                <div class="text-xs opacity-80">蓝紫渐变</div>
              </div>
            </div>
            <div class="theme-option" data-theme="green" onclick="selectTheme('green')">
              <div class="bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl p-4 text-white mb-2">
                <div class="text-sm font-medium">清新主题</div>
                <div class="text-xs opacity-80">绿色系</div>
              </div>
            </div>
            <div class="theme-option" data-theme="orange" onclick="selectTheme('orange')">
              <div class="bg-gradient-to-r from-orange-500 to-red-500 rounded-xl p-4 text-white mb-2">
                <div class="text-sm font-medium">活力主题</div>
                <div class="text-xs opacity-80">橙红渐变</div>
              </div>
            </div>
            <div class="theme-option" data-theme="dark" onclick="selectTheme('dark')">
              <div class="bg-gradient-to-r from-gray-800 to-gray-900 rounded-xl p-4 text-white mb-2">
                <div class="text-sm font-medium">深色主题</div>
                <div class="text-xs opacity-80">护眼模式</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 功能开关面板 -->
      <div id="function-panel" class="tab-panel hidden">
        <div class="bg-white/60 backdrop-blur-md rounded-2xl p-6 mb-6 border border-white/20">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">功能开关</h3>
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium text-gray-900">推送通知</div>
                <div class="text-sm text-gray-500">接收重要市场消息</div>
              </div>
              <div class="toggle-switch active" onclick="toggleFunction(this, 'notifications')"></div>
            </div>
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium text-gray-900">自动刷新</div>
                <div class="text-sm text-gray-500">自动更新股价数据</div>
              </div>
              <div class="toggle-switch active" onclick="toggleFunction(this, 'autoRefresh')"></div>
            </div>
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium text-gray-900">声音提醒</div>
                <div class="text-sm text-gray-500">价格变动声音提示</div>
              </div>
              <div class="toggle-switch" onclick="toggleFunction(this, 'soundAlert')"></div>
            </div>
          </div>
        </div>
      </div>

    </main>

    <!-- 完成按钮 -->
    <div class="fixed bottom-20 left-4 right-4">
      <button onclick="saveModules()" class="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold py-4 rounded-2xl text-lg shadow-lg hover:shadow-xl transition-all transform hover:scale-105">
        <i class="ri-check-line mr-2"></i>保存设置
      </button>
    </div>

    <!-- 底部导航栏 -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
      <div class="grid grid-cols-4 h-16">
        <button onclick="window.location.href='index.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-apps-2-line text-lg text-gray-500"></i>
          </div>
          <span class="text-xs text-gray-500">首页</span>
        </button>
        <button onclick="window.location.href='自选股.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-star-line text-lg text-gray-500"></i>
          </div>
          <span class="text-xs text-gray-500">自选股</span>
        </button>
        <button onclick="window.location.href='reports.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-file-list-line text-lg text-gray-500"></i>
          </div>
          <span class="text-xs text-gray-500">报告中心</span>
        </button>
        <button onclick="window.location.href='我的.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-user-line text-lg text-gray-500"></i>
          </div>
          <span class="text-xs text-gray-500">我的</span>
        </button>
      </div>
    </nav>

    <script>
      // 当前活动的选项卡
      let currentTab = 'layout';

      // 切换选项卡
      function switchTab(tabName) {
        // 更新选项卡按钮状态
        document.querySelectorAll('.tab-btn').forEach(btn => {
          btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 显示对应面板
        document.querySelectorAll('.tab-panel').forEach(panel => {
          panel.classList.add('hidden');
        });
        document.getElementById(`${tabName}-panel`).classList.remove('hidden');

        currentTab = tabName;
      }

      // 切换拖动模式
      function toggleDragMode(element) {
        element.classList.toggle('active');
        const isActive = element.classList.contains('active');

        // 更新模块卡片的拖动状态
        const moduleCards = document.querySelectorAll('.module-card');
        moduleCards.forEach(card => {
          if (isActive) {
            card.style.cursor = 'grab';
          } else {
            card.style.cursor = 'default';
          }
        });
      }

      // 添加模块
      function addModule(button) {
        const moduleCard = button.closest('.module-card');
        const moduleName = moduleCard.dataset.module;

        // 更新按钮样式和功能
        button.className = 'remove-btn';
        button.textContent = '-';
        button.onclick = function() { removeModule(this); };

        // 更新卡片样式
        moduleCard.classList.add('selected');
        moduleCard.className = moduleCard.className.replace('bg-white/60', 'bg-white/80');
        moduleCard.className = moduleCard.className.replace('border-gray-200', getModuleBorderColor(moduleName));

        // 移动到已选中区域
        const selectedModules = document.getElementById('selected-modules');
        selectedModules.appendChild(moduleCard);

        // 添加动画效果
        moduleCard.style.animation = 'slideIn 0.4s ease-out';

        // 更新计数
        updateModuleCounts();
      }

      // 移除模块
      function removeModule(button) {
        const moduleCard = button.closest('.module-card');
        const moduleName = moduleCard.dataset.module;

        // 更新按钮样式和功能
        button.className = 'add-btn';
        button.textContent = '+';
        button.onclick = function() { addModule(this); };

        // 更新卡片样式
        moduleCard.classList.remove('selected');
        moduleCard.className = moduleCard.className.replace('bg-white/80', 'bg-white/60');
        moduleCard.className = moduleCard.className.replace(getModuleBorderColor(moduleName), 'border-gray-200');

        // 移动到已隐藏区域
        const hiddenModules = document.getElementById('hidden-modules');
        hiddenModules.appendChild(moduleCard);

        // 添加动画效果
        moduleCard.style.animation = 'slideIn 0.4s ease-out';

        // 更新计数
        updateModuleCounts();
      }

      // 获取模块边框颜色
      function getModuleBorderColor(moduleName) {
        const colorMap = {
          '今日投资思考': 'border-purple-200',
          'AI今日推荐': 'border-blue-200',
          '自选股': 'border-yellow-200',
          '最新资讯': 'border-green-200',
          '模拟盘': 'border-indigo-200',
          '个性化定制': 'border-pink-200'
        };
        return colorMap[moduleName] || 'border-gray-200';
      }

      // 更新模块计数
      function updateModuleCounts() {
        const selectedCount = document.querySelectorAll('#selected-modules .module-card').length;
        const hiddenCount = document.querySelectorAll('#hidden-modules .module-card').length;

        document.querySelector('.text-green-700').textContent = `${selectedCount}个`;
        document.querySelector('.text-gray-700').textContent = `${hiddenCount}个`;
      }

      // 选择主题
      function selectTheme(themeName) {
        document.querySelectorAll('.theme-option').forEach(option => {
          option.classList.remove('active');
        });
        document.querySelector(`[data-theme="${themeName}"]`).classList.add('active');

        // 保存主题选择
        localStorage.setItem('selectedTheme', themeName);

        // 显示主题应用提示
        showToast('主题已选择，保存后生效');
      }

      // 切换功能开关
      function toggleFunction(element, functionName) {
        element.classList.toggle('active');
        const isActive = element.classList.contains('active');

        // 保存功能状态
        localStorage.setItem(`function_${functionName}`, isActive);

        showToast(`${getFunctionDisplayName(functionName)}已${isActive ? '开启' : '关闭'}`);
      }

      // 获取功能显示名称
      function getFunctionDisplayName(functionName) {
        const nameMap = {
          'notifications': '推送通知',
          'autoRefresh': '自动刷新',
          'soundAlert': '声音提醒'
        };
        return nameMap[functionName] || functionName;
      }

      // 预览更改
      function previewChanges() {
        showToast('预览功能开发中...');
      }

      // 显示提示消息
      function showToast(message) {
        // 创建提示元素
        const toast = document.createElement('div');
        toast.className = 'fixed top-20 left-1/2 transform -translate-x-1/2 bg-black/80 text-white px-4 py-2 rounded-lg text-sm z-50';
        toast.textContent = message;

        document.body.appendChild(toast);

        // 3秒后移除
        setTimeout(() => {
          toast.remove();
        }, 3000);
      }

      // 保存模块配置
      function saveModules() {
        const selectedModules = document.querySelectorAll('#selected-modules .module-card');
        const moduleOrder = Array.from(selectedModules).map(card => card.dataset.module);

        // 保存模块配置
        localStorage.setItem('moduleOrder', JSON.stringify(moduleOrder));

        // 保存当前主题
        const selectedTheme = document.querySelector('.theme-option.active')?.dataset.theme || 'default';
        localStorage.setItem('selectedTheme', selectedTheme);

        // 保存功能开关状态
        const functionSwitches = document.querySelectorAll('#function-panel .toggle-switch');
        functionSwitches.forEach(switchEl => {
          const functionName = switchEl.getAttribute('onclick').match(/toggleFunction\(this, '(.+?)'\)/)?.[1];
          if (functionName) {
            localStorage.setItem(`function_${functionName}`, switchEl.classList.contains('active'));
          }
        });

        // 显示保存成功动画
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="ri-check-line mr-2"></i>保存成功！';
        button.style.background = 'linear-gradient(135deg, #10b981, #059669)';

        setTimeout(() => {
          button.innerHTML = originalText;
          button.style.background = 'linear-gradient(135deg, #a855f7, #ec4899)';

          // 返回首页
          window.location.href = 'index.html';
        }, 1500);
      }

      // 返回功能
      function goBack() {
        if (window.history.length > 1) {
          window.history.back();
        } else {
          window.location.href = 'index.html';
        }
      }

      // 分享功能
      function shareApp() {
        if (navigator.share) {
          navigator.share({
            title: '金融智能体 - 编辑模块',
            text: '自定义首页模块布局',
            url: window.location.href
          }).catch(err => {
            console.log('分享失败:', err);
          });
        } else {
          if (navigator.clipboard) {
            navigator.clipboard.writeText(window.location.href).then(() => {
              alert('链接已复制到剪贴板');
            });
          }
        }
      }

      // 页面加载时的初始化
      document.addEventListener('DOMContentLoaded', function() {
        // 加载保存的配置
        loadSavedConfigurations();

        // 初始化模块卡片动画
        const moduleCards = document.querySelectorAll('.module-card');
        moduleCards.forEach((card, index) => {
          card.style.animationDelay = `${index * 0.1}s`;
          card.style.animation = 'fadeInUp 0.5s ease-out forwards';
        });

        // 更新模块计数
        updateModuleCounts();
      });

      // 加载保存的配置
      function loadSavedConfigurations() {
        // 加载主题配置
        const savedTheme = localStorage.getItem('selectedTheme') || 'default';
        const themeOption = document.querySelector(`[data-theme="${savedTheme}"]`);
        if (themeOption) {
          themeOption.classList.add('active');
        }

        // 加载功能开关状态
        const functionNames = ['notifications', 'autoRefresh', 'soundAlert'];
        functionNames.forEach(name => {
          const saved = localStorage.getItem(`function_${name}`);
          if (saved !== null) {
            const switchEl = document.querySelector(`[onclick*="${name}"]`);
            if (switchEl) {
              if (saved === 'true') {
                switchEl.classList.add('active');
              } else {
                switchEl.classList.remove('active');
              }
            }
          }
        });
      }

      // 添加CSS动画
      const style = document.createElement('style');
      style.textContent = `
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes slideIn {
          from {
            opacity: 0;
            transform: translateX(-20px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }
      `;
      document.head.appendChild(style);
    </script>
  </body>
</html>
