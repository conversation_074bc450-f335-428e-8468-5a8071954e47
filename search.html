<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>搜索 - 金融智能体</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: "#3B82F6",
            secondary: "#F1F5F9",
          },
        },
      },
    };
  </script>
  <style>
    .search-input:focus {
      border-color: #3B82F6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    .hot-stock-item {
      transition: all 0.2s ease;
    }
    .hot-stock-item:hover {
      background: #f8fafc;
      transform: translateY(-1px);
    }
    .gradient-bg {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  </style>
</head>
<body class="bg-gray-50 min-h-screen">
  <!-- 顶部导航栏 -->
  <nav class="fixed top-0 w-full bg-white z-50 px-4 py-3 border-b border-gray-100">
    <div class="flex items-center justify-between">
      <div class="flex items-center flex-1">
        <button onclick="goBack()" class="w-8 h-8 flex items-center justify-center mr-3 cursor-pointer">
          <i class="ri-arrow-left-line text-gray-600 text-lg"></i>
        </button>
        <div class="flex-1 relative">
          <i class="ri-search-line text-gray-400 text-lg absolute left-3 top-1/2 transform -translate-y-1/2"></i>
          <input
            type="text"
            id="search-input"
            placeholder="搜索股票、新闻..."
            class="w-full pl-10 pr-4 py-2 bg-gray-100 rounded-lg border-0 outline-none text-sm search-input"
            autocomplete="off"
          >
        </div>
        <!-- 消息中心按钮 -->
        <button onclick="openMessageCenter()" class="w-8 h-8 flex items-center justify-center relative ml-2">
          <i class="ri-notification-line text-gray-600 text-lg"></i>
          <!-- 未读消息提示点 -->
          <div class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
        </button>
      </div>
    </div>
  </nav>

  <!-- 主要内容 -->
  <main class="pt-20 pb-20 px-4">
    <!-- AI智能体 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
        <div class="flex items-center space-x-3 cursor-pointer" onclick="openAIAnalysis()">
          <div class="w-12 h-12 gradient-bg rounded-full flex items-center justify-center">
            <i class="ri-robot-line text-white text-xl"></i>
          </div>
          <div class="flex-1">
            <div class="text-gray-900 font-medium">AI智能体分析</div>
            <div class="text-gray-500 text-sm">点击开始与AI对话，获取专业投资建议</div>
          </div>
          <i class="ri-arrow-right-s-line text-gray-400"></i>
        </div>
      </div>
    </section>

    <!-- 快速功能 -->
    <section class="mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-3">快速功能</h3>
      <div class="grid grid-cols-2 gap-3">
        <button class="bg-white rounded-xl p-4 shadow-sm border border-gray-100 flex items-center space-x-3 hover:shadow-md transition-shadow">
          <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <i class="ri-line-chart-line text-blue-600 text-lg"></i>
          </div>
          <div class="text-left">
            <div class="text-gray-900 font-medium text-sm">技术分析</div>
            <div class="text-gray-500 text-xs">K线图表分析</div>
          </div>
        </button>
        <button class="bg-white rounded-xl p-4 shadow-sm border border-gray-100 flex items-center space-x-3 hover:shadow-md transition-shadow">
          <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
            <i class="ri-search-eye-line text-green-600 text-lg"></i>
          </div>
          <div class="text-left">
            <div onclick="window.location.href='AI选股.html'" class="text-gray-900 font-medium text-sm">选股器</div>
            <div class="text-gray-500 text-xs">智能选股工具</div>
          </div>
        </button>
      </div>
    </section>

    <!-- 热门股票 -->
    <section class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">热门股票</h3>
        <button class="text-primary text-sm flex items-center space-x-1">
          <span>查看更多</span>
          <i class="ri-arrow-right-s-line"></i>
        </button>
      </div>

      <div class="bg-white rounded-xl shadow-sm border border-gray-100">
        <div class="space-y-0" id="hot-stocks">
          <!-- 热门股票列表 -->
          <div class="hot-stock-item flex items-center justify-between p-4 border-b border-gray-100 cursor-pointer">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                <span class="text-red-600 font-bold text-sm">1</span>
              </div>
              <div>
                <div class="text-gray-900 font-medium">贵州茅台</div>
                <div class="text-gray-500 text-xs">600519</div>
              </div>
            </div>
            <div class="text-right">
              <div class="text-gray-900 font-medium">¥1,678.50</div>
              <div class="text-red-600 text-sm">*****%</div>
            </div>
          </div>

          <div class="hot-stock-item flex items-center justify-between p-4 border-b border-gray-100 cursor-pointer">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                <span class="text-orange-600 font-bold text-sm">2</span>
              </div>
              <div>
                <div class="text-gray-900 font-medium">比亚迪</div>
                <div class="text-gray-500 text-xs">002594</div>
              </div>
            </div>
            <div class="text-right">
              <div class="text-gray-900 font-medium">¥245.80</div>
              <div class="text-red-600 text-sm">*****%</div>
            </div>
          </div>

          <div class="hot-stock-item flex items-center justify-between p-4 border-b border-gray-100 cursor-pointer">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <span class="text-yellow-600 font-bold text-sm">3</span>
              </div>
              <div>
                <div class="text-gray-900 font-medium">宁德时代</div>
                <div class="text-gray-500 text-xs">300750</div>
              </div>
            </div>
            <div class="text-right">
              <div class="text-gray-900 font-medium">¥185.20</div>
              <div class="text-green-600 text-sm">-0.65%</div>
            </div>
          </div>

          <div class="hot-stock-item flex items-center justify-between p-4 border-b border-gray-100 cursor-pointer">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <span class="text-gray-600 font-bold text-sm">4</span>
              </div>
              <div>
                <div class="text-gray-900 font-medium">五粮液</div>
                <div class="text-gray-500 text-xs">000858</div>
              </div>
            </div>
            <div class="text-right">
              <div class="text-gray-900 font-medium">¥158.20</div>
              <div class="text-red-600 text-sm">*****%</div>
            </div>
          </div>

          <div class="hot-stock-item flex items-center justify-between p-4 cursor-pointer">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <span class="text-gray-600 font-bold text-sm">5</span>
              </div>
              <div>
                <div class="text-gray-900 font-medium">腾讯控股</div>
                <div class="text-gray-500 text-xs">00700</div>
              </div>
            </div>
            <div class="text-right">
              <div class="text-gray-900 font-medium">¥385.60</div>
              <div class="text-red-600 text-sm">+0.52%</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 搜索结果区域（隐藏，搜索时显示） -->
    <section id="search-results" class="hidden">
      <div class="mb-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-3">搜索结果</h3>
        <div id="search-results-list" class="space-y-3">
          <!-- 搜索结果将在这里动态加载 -->
        </div>
      </div>
    </section>
  </main>

  <script>
    let searchTimeout;
    const searchInput = document.getElementById('search-input');
    const searchResults = document.getElementById('search-results');
    const searchResultsList = document.getElementById('search-results-list');
    const hotStocksSection = document.querySelector('section:last-of-type');

    // 模拟股票和新闻数据
    const stockData = [
      { name: '贵州茅台', code: '600519', market: 'A股', price: '1678.50', change: '*****%' },
      { name: '比亚迪', code: '002594', market: 'A股', price: '245.80', change: '*****%' },
      { name: '宁德时代', code: '300750', market: 'A股', price: '185.20', change: '-0.65%' },
      { name: '五粮液', code: '000858', market: 'A股', price: '158.20', change: '*****%' },
      { name: '小米集团-W', code: 'HK01810', market: '港股', price: '12.50', change: '-1.80%' },
      { name: '腾讯控股', code: 'HK00700', market: '港股', price: '385.60', change: '+0.52%' },
      { name: '苹果', code: 'AAPL', market: '美股', price: '175.43', change: '*****%' },
      { name: '特斯拉', code: 'TSLA', market: '美股', price: '248.50', change: '-2.15%' }
    ];

    const newsData = [
      { title: '央行降准释放流动性，银行股集体上涨', category: '重要资讯', time: '2小时前' },
      { title: '新能源汽车销量创新高，相关概念股活跃', category: '行业资讯', time: '4小时前' },
      { title: '贵州茅台三季度业绩超预期', category: '公司公告', time: '1小时前' },
      { title: 'A股三大指数集体收涨', category: '市场快讯', time: '6小时前' },
      { title: '证监会发布新规范上市公司信息披露', category: '政策解读', time: '8小时前' }
    ];

    // 搜索功能
    searchInput.addEventListener('input', function() {
      const query = this.value.trim();
      
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        if (query.length > 0) {
          performSearch(query);
        } else {
          hideSearchResults();
        }
      }, 300);
    });

    function performSearch(query) {
      const stockResults = stockData.filter(stock => 
        stock.name.includes(query) || 
        stock.code.includes(query.toUpperCase())
      );

      const newsResults = newsData.filter(news => 
        news.title.includes(query) || 
        news.category.includes(query)
      );

      displaySearchResults(stockResults, newsResults, query);
    }

    function displaySearchResults(stocks, news, query) {
      let resultsHTML = '';

      if (stocks.length > 0) {
        resultsHTML += '<div class="mb-4"><h4 class="text-gray-600 text-sm mb-3 font-medium">股票</h4>';
        resultsHTML += '<div class="bg-white rounded-xl shadow-sm border border-gray-100">';
        stocks.forEach((stock, index) => {
          const changeColor = stock.change.startsWith('+') ? 'text-red-600' : 'text-green-600';
          const borderClass = index === stocks.length - 1 ? '' : 'border-b border-gray-100';
          resultsHTML += `
            <div class="flex items-center justify-between p-4 ${borderClass} cursor-pointer hover:bg-gray-50 transition-colors" onclick="selectStock('${stock.code}', '${stock.name}')">
              <div>
                <div class="text-gray-900 font-medium">${highlightQuery(stock.name, query)}</div>
                <div class="text-gray-500 text-xs">${stock.market} ${stock.code}</div>
              </div>
              <div class="text-right">
                <div class="text-gray-900 font-medium">${stock.price}</div>
                <div class="${changeColor} text-sm">${stock.change}</div>
              </div>
            </div>
          `;
        });
        resultsHTML += '</div></div>';
      }

      if (news.length > 0) {
        resultsHTML += '<div class="mb-4"><h4 class="text-gray-600 text-sm mb-3 font-medium">资讯</h4>';
        resultsHTML += '<div class="bg-white rounded-xl shadow-sm border border-gray-100">';
        news.forEach((newsItem, index) => {
          const borderClass = index === news.length - 1 ? '' : 'border-b border-gray-100';
          resultsHTML += `
            <div class="p-4 ${borderClass} cursor-pointer hover:bg-gray-50 transition-colors" onclick="openNews('${newsItem.title}')">
              <div class="text-gray-900 font-medium mb-2">${highlightQuery(newsItem.title, query)}</div>
              <div class="flex items-center space-x-2 text-xs">
                <span class="text-primary bg-blue-50 px-2 py-1 rounded">${newsItem.category}</span>
                <span class="text-gray-500">${newsItem.time}</span>
              </div>
            </div>
          `;
        });
        resultsHTML += '</div></div>';
      }

      if (stocks.length === 0 && news.length === 0) {
        resultsHTML = `
          <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-8 text-center">
            <i class="ri-search-line text-4xl text-gray-300 mb-4"></i>
            <div class="text-gray-600 font-medium mb-1">未找到相关结果</div>
            <div class="text-gray-500 text-sm">试试其他关键词或检查拼写</div>
          </div>
        `;
      }

      searchResultsList.innerHTML = resultsHTML;
      showSearchResults();
    }

    function highlightQuery(text, query) {
      if (!query) return text;
      const regex = new RegExp(`(${query})`, 'gi');
      return text.replace(regex, '<span class="bg-yellow-500 text-black px-1 rounded">$1</span>');
    }

    function showSearchResults() {
      searchResults.classList.remove('hidden');
      // 隐藏AI智能体和快速功能区域
      document.querySelector('section:nth-child(1)').classList.add('hidden');
      document.querySelector('section:nth-child(2)').classList.add('hidden');
      hotStocksSection.classList.add('hidden');
    }

    function hideSearchResults() {
      searchResults.classList.add('hidden');
      // 显示AI智能体和快速功能区域
      document.querySelector('section:nth-child(1)').classList.remove('hidden');
      document.querySelector('section:nth-child(2)').classList.remove('hidden');
      hotStocksSection.classList.remove('hidden');
    }

    function selectStock(code, name) {
      // 跳转到股票详情页
      window.location.href = `股票分析.html?code=${code}&name=${encodeURIComponent(name)}`;
    }

    function openNews(title) {
      // 跳转到新闻详情页
      alert(`打开新闻：${title}`);
    }

    function goBack() {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        window.location.href = 'index.html';
      }
    }

    function openMessageCenter() {
      window.location.href = '消息中心.html';
    }

    function openAIAnalysis() {
      window.location.href = '智能体分析.html';
    }

    // 点击热门股票
    document.querySelectorAll('.hot-stock-item').forEach(item => {
      item.addEventListener('click', function() {
        const stockName = this.querySelector('.text-white.font-medium').textContent;
        const stockCode = this.querySelector('.text-gray-400.text-xs').textContent.split(' ')[1];
        selectStock(stockCode, stockName);
      });
    });
  </script>
</body>
</html>
