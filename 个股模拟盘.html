<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>个股模拟盘 - 金融智能体</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: "#3B82F6",
            secondary: "#F1F5F9",
          },
        },
      },
    };
  </script>
  <style>
    .gradient-bg { 
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .stock-up { color: #ef4444; }
    .stock-down { color: #22c55e; }
    .profit-bg { background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%); }
    .loss-bg { background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%); }
    .trade-card {
      transition: all 0.3s ease;
    }
    .trade-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
  </style>
</head>
<body class="bg-gray-50 min-h-screen">
  <!-- 顶部导航栏 -->
  <nav class="fixed top-0 w-full bg-white z-50 px-4 py-3 border-b border-gray-100">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <button onclick="goBack()" class="w-8 h-8 flex items-center justify-center mr-3 cursor-pointer">
          <i class="ri-arrow-left-line text-gray-600 text-lg"></i>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">个股模拟盘</h1>
      </div>
      <div class="flex items-center space-x-2">
        <!-- 智能体分析按钮 -->
        <button onclick="openAIChat()" class="w-8 h-8 flex items-center justify-center">
          <i class="ri-robot-line text-gray-600 text-lg"></i>
        </button>
        <!-- 微信小程序分享按钮 -->
        <button onclick="shareSimulation()" class="w-8 h-8 flex items-center justify-center">
          <div class="flex space-x-0.5">
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
          </div>
        </button>
        <!-- 微信小程序返回主界面按钮 -->
        <button class="w-8 h-8 flex items-center justify-center">
          <div class="w-5 h-5 border-2 border-gray-600 rounded-full flex items-center justify-center">
            <div class="w-2 h-2 bg-gray-600 rounded-full"></div>
          </div>
        </button>
      </div>
    </div>
  </nav>

  <!-- 主要内容区域 -->
  <main class="pt-16 pb-20 px-4">
    <!-- 股票信息卡片 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center space-x-3">
            <h2 id="stock-name" class="text-lg font-bold text-gray-900">贵州茅台</h2>
            <span id="stock-code" class="text-sm text-gray-500">600519</span>
          </div>
          <div class="text-right">
            <div id="current-price" class="text-xl font-bold text-gray-900">¥1,678.50</div>
            <div id="price-change" class="text-sm stock-up font-medium">+39.50 (+2.35%)</div>
          </div>
        </div>
        
        <!-- 模拟盘总览 -->
        <div class="grid grid-cols-3 gap-3 mt-4">
          <div class="text-center p-3 bg-gray-50 rounded-lg">
            <div id="total-shares" class="text-lg font-bold text-gray-900">500</div>
            <div class="text-xs text-gray-500">持仓股数</div>
          </div>
          <div class="text-center p-3 bg-gray-50 rounded-lg">
            <div id="avg-cost" class="text-lg font-bold text-gray-900">¥1,620</div>
            <div class="text-xs text-gray-500">平均成本</div>
          </div>
          <div class="text-center p-3 profit-bg rounded-lg">
            <div id="total-profit" class="text-lg font-bold text-green-600">+¥29,250</div>
            <div class="text-xs text-green-700">总盈亏</div>
          </div>
        </div>
      </div>
    </section>

    <!-- AI交易状态 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <h3 class="font-semibold text-gray-900 mb-4">
          <i class="ri-robot-line text-primary mr-2"></i>AI自动交易状态
        </h3>

        <!-- AI交易开关状态 -->
        <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 mb-4">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse"></div>
              <span class="font-medium text-gray-900">AI自动交易已启用</span>
            </div>
            <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">运行中</span>
          </div>
          <p class="text-sm text-gray-600">
            AI正在根据实时市场数据和技术分析自动执行交易策略，无需人工干预。
          </p>
        </div>

        <!-- 当前AI策略 -->
        <div class="space-y-3">
          <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div class="flex items-center">
              <i class="ri-brain-line text-purple-500 mr-2"></i>
              <span class="text-sm font-medium text-gray-900">当前策略</span>
            </div>
            <span class="text-sm text-gray-700">趋势跟踪 + 均值回归</span>
          </div>

          <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div class="flex items-center">
              <i class="ri-time-line text-blue-500 mr-2"></i>
              <span class="text-sm font-medium text-gray-900">分析频率</span>
            </div>
            <span class="text-sm text-gray-700">每5分钟更新一次</span>
          </div>

          <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div class="flex items-center">
              <i class="ri-shield-check-line text-green-500 mr-2"></i>
              <span class="text-sm font-medium text-gray-900">风控状态</span>
            </div>
            <span class="text-sm text-green-700">正常</span>
          </div>
        </div>
      </div>
    </section>

    <!-- 持仓详情 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <h3 class="font-semibold text-gray-900 mb-4">
          <i class="ri-pie-chart-line text-primary mr-2"></i>持仓详情
        </h3>
        
        <div class="space-y-3">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">持仓市值</span>
            <span id="market-value" class="font-medium text-gray-900">¥839,250</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">持仓成本</span>
            <span id="cost-value" class="font-medium text-gray-900">¥810,000</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">浮动盈亏</span>
            <span id="floating-profit" class="font-medium text-green-600">+¥29,250 (+3.61%)</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">今日盈亏</span>
            <span id="today-profit" class="font-medium text-green-600">+¥19,750 (+2.35%)</span>
          </div>
        </div>
        
        <!-- 盈亏分析图表占位 -->
        
      </div>
    </section>

    <!-- AI交易记录 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-semibold text-gray-900">
            <i class="ri-history-line text-primary mr-2"></i>AI交易记录
          </h3>
          <button class="text-sm text-primary">查看全部</button>
        </div>

        <div class="space-y-3">
          <!-- AI交易记录项 -->
          <div class="trade-card p-3 border border-gray-200 rounded-lg">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-2">
                <span class="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">AI买入</span>
                <span class="text-sm font-medium text-gray-900">100股</span>
                <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">技术突破</span>
              </div>
              <div class="text-right">
                <div class="text-sm font-medium text-gray-900">¥1,650.00</div>
                <div class="text-xs text-gray-500">2024-01-15 09:30</div>
              </div>
            </div>
            <div class="flex justify-between text-xs text-gray-500 mb-2">
              <span>成交金额：¥165,000</span>
              <span class="text-green-600">当前盈亏：+¥2,850</span>
            </div>
            <div class="text-xs text-gray-600 bg-gray-50 rounded p-2">
              <i class="ri-robot-line mr-1"></i>AI分析：MACD金叉，成交量放大，突破关键阻力位，执行买入策略
            </div>
          </div>

          <div class="trade-card p-3 border border-gray-200 rounded-lg">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-2">
                <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">AI卖出</span>
                <span class="text-sm font-medium text-gray-900">50股</span>
                <span class="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded">获利了结</span>
              </div>
              <div class="text-right">
                <div class="text-sm font-medium text-gray-900">¥1,680.00</div>
                <div class="text-xs text-gray-500">2024-01-14 14:30</div>
              </div>
            </div>
            <div class="flex justify-between text-xs text-gray-500 mb-2">
              <span>成交金额：¥84,000</span>
              <span class="text-green-600">实现盈利：+¥3,000</span>
            </div>
            <div class="text-xs text-gray-600 bg-gray-50 rounded p-2">
              <i class="ri-robot-line mr-1"></i>AI分析：达到目标价位，RSI超买信号，执行部分获利了结策略
            </div>
          </div>

          <div class="trade-card p-3 border border-gray-200 rounded-lg">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-2">
                <span class="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">AI买入</span>
                <span class="text-sm font-medium text-gray-900">200股</span>
                <span class="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">逢低加仓</span>
              </div>
              <div class="text-right">
                <div class="text-sm font-medium text-gray-900">¥1,600.00</div>
                <div class="text-xs text-gray-500">2024-01-12 10:15</div>
              </div>
            </div>
            <div class="flex justify-between text-xs text-gray-500 mb-2">
              <span>成交金额：¥320,000</span>
              <span class="text-green-600">当前盈亏：+¥15,700</span>
            </div>
            <div class="text-xs text-gray-600 bg-gray-50 rounded p-2">
              <i class="ri-robot-line mr-1"></i>AI分析：回调至支撑位，KDJ低位金叉，基本面良好，执行加仓策略
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- AI实时分析 -->
    <section class="mb-6">
      <div class="bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl p-4 border border-purple-200">
        <h3 class="font-semibold text-gray-900 mb-3">
          <i class="ri-robot-line text-purple-500 mr-2"></i>AI实时分析与策略
        </h3>

        <div class="space-y-4">
          <!-- 当前市场判断 -->
          <div class="bg-white rounded-lg p-3">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center">
                <i class="ri-eye-line text-blue-500 mr-2"></i>
                <span class="text-sm font-medium text-gray-900">当前市场判断</span>
              </div>
              <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">看涨</span>
            </div>
            <p class="text-sm text-gray-700">
              技术面：MACD金叉向上，RSI处于强势区间，成交量持续放大，短期趋势向好。
            </p>
          </div>

          <!-- 下一步AI策略 -->
          <div class="bg-white rounded-lg p-3">
            <div class="flex items-center mb-2">
              <i class="ri-compass-line text-purple-500 mr-2"></i>
              <span class="text-sm font-medium text-gray-900">下一步AI策略</span>
            </div>
            <div class="space-y-2">
              <div class="flex items-center text-sm text-gray-700">
                <i class="ri-arrow-right-s-line text-gray-400 mr-1"></i>
                <span>如果价格回调至¥1,650-1,660，AI将自动执行加仓操作</span>
              </div>
              <div class="flex items-center text-sm text-gray-700">
                <i class="ri-arrow-right-s-line text-gray-400 mr-1"></i>
                <span>如果突破¥1,720阻力位，AI将继续持有并设置新的止盈位</span>
              </div>
              <div class="flex items-center text-sm text-gray-700">
                <i class="ri-arrow-right-s-line text-gray-400 mr-1"></i>
                <span>如果跌破¥1,580支撑位，AI将执行止损策略</span>
              </div>
            </div>
          </div>

          <!-- 关键价位监控 -->
          <div class="grid grid-cols-3 gap-3">
            <div class="bg-white rounded-lg p-3 text-center">
              <div class="text-xs text-gray-600 mb-1">AI加仓位</div>
              <div class="text-sm font-bold text-blue-600">¥1,655</div>
            </div>
            <div class="bg-white rounded-lg p-3 text-center">
              <div class="text-xs text-gray-600 mb-1">AI止盈位</div>
              <div class="text-sm font-bold text-green-600">¥1,750</div>
            </div>
            <div class="bg-white rounded-lg p-3 text-center">
              <div class="text-xs text-gray-600 mb-1">AI止损位</div>
              <div class="text-sm font-bold text-red-600">¥1,580</div>
            </div>
          </div>

          <!-- AI信心指数 -->
          <div class="bg-white rounded-lg p-3">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-gray-900">AI策略信心指数</span>
              <span class="text-sm font-bold text-green-600">85%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-green-500 h-2 rounded-full" style="width: 85%"></div>
            </div>
            <p class="text-xs text-gray-600 mt-2">基于多维度技术指标和基本面分析的综合评分</p>
          </div>
        </div>
      </div>
    </section>
  </main>

  <script>
    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 从URL参数获取股票信息
      const urlParams = new URLSearchParams(window.location.search);
      const stockCode = urlParams.get('code') || '600519';
      const stockName = urlParams.get('name') || '贵州茅台';
      
      // 更新页面内容
      updateStockInfo(stockCode, stockName);

      // 初始化AI状态监控
      initAIMonitoring();
    });

    // 更新股票信息
    function updateStockInfo(stockCode, stockName) {
      document.getElementById('stock-name').textContent = stockName;
      document.getElementById('stock-code').textContent = stockCode;
      document.title = `${stockName}模拟盘 - 金融智能体`;
    }

    // 初始化AI状态监控
    function initAIMonitoring() {
      // 模拟AI状态更新
      setInterval(updateAIStatus, 30000); // 每30秒更新一次AI状态

      // 初始化时显示AI状态
      updateAIStatus();
    }

    // 更新AI状态
    function updateAIStatus() {
      // 模拟AI分析更新
      const analysisTime = new Date().toLocaleTimeString();
      console.log(`AI分析更新时间: ${analysisTime}`);

      // 这里可以添加实际的AI状态更新逻辑
      // 比如更新信心指数、策略状态等
    }

    // 模拟AI交易执行
    function simulateAITrade() {
      // 这个函数可以用来模拟AI自动执行交易
      // 在实际应用中，这会连接到后端AI交易系统
      const trades = [
        { type: 'buy', amount: 100, price: 1650, reason: '技术突破' },
        { type: 'sell', amount: 50, price: 1680, reason: '获利了结' },
        { type: 'buy', amount: 200, price: 1600, reason: '逢低加仓' }
      ];

      return trades;
    }

    // 返回功能
    function goBack() {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        window.location.href = 'index.html';
      }
    }

    // 打开AI智能体分析
    function openAIChat() {
      window.location.href = 'AI选股.html#ai-chat';
    }

    // 分享模拟盘
    function shareSimulation() {
      const stockName = document.getElementById('stock-name').textContent;
      
      if (navigator.share) {
        navigator.share({
          title: `${stockName}模拟盘 - 金融智能体`,
          text: `查看我的${stockName}模拟交易记录`,
          url: window.location.href
        }).catch(err => {
          console.log('分享失败:', err);
        });
      } else {
        if (navigator.clipboard) {
          navigator.clipboard.writeText(window.location.href).then(() => {
            alert('模拟盘链接已复制到剪贴板');
          });
        }
      }
    }
  </script>
</body>
</html>
