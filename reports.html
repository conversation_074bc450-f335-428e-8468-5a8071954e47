<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>报告中心 - AI股票分析师</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: "#3B82F6",
            secondary: "#F1F5F9",
          },
        },
      },
    };
  </script>
  <style>
    .gradient-bg { 
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  </style>
</head>
<body class="bg-gray-50 min-h-screen">
  <!-- 顶部导航栏 -->
  <nav class="fixed top-0 w-full bg-white z-50 px-4 py-3 border-b border-gray-100">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <button onclick="goBack()" class="w-8 h-8 flex items-center justify-center mr-3 cursor-pointer">
          <i class="ri-arrow-left-line text-gray-600 text-lg"></i>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">报告中心</h1>
      </div>
      <div class="flex items-center space-x-2">
        <!-- 智能体分析按钮 -->
        <button onclick="window.location.href='智能体分析.html'" class="w-8 h-8 flex items-center justify-center">
          <i class="ri-robot-line text-gray-600 text-lg"></i>
        </button>
        <!-- 微信小程序分享按钮 -->
        <button onclick="shareApp()" class="w-8 h-8 flex items-center justify-center">
          <div class="flex space-x-0.5">
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
          </div>
        </button>
        <!-- 微信小程序返回主界面按钮 -->
        <button class="w-8 h-8 flex items-center justify-center">
          <div class="w-5 h-5 border-2 border-gray-600 rounded-full flex items-center justify-center">
            <div class="w-2 h-2 bg-gray-600 rounded-full"></div>
          </div>
        </button>
      </div>
    </div>
  </nav>

  <!-- 主要内容区域 -->
  <main class="pt-16 pb-20 px-4">


    

    <!-- 股票选择器 -->
    <section class="mb-4">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="flex items-center justify-between">
          <h3 class="font-semibold text-gray-900">分析报告</h3>
          <div class="relative">
            <select id="stockSelector" onchange="filterByStock()" class="bg-gray-50 border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary">
              <option value="all">全部自选股</option>
              <option value="600519" selected>贵州茅台 (仓位最高)</option>
              <option value="002594">比亚迪</option>
              <option value="300750">宁德时代</option>
              <option value="601318">中国平安</option>
            </select>
          </div>
        </div>
      </div>
    </section>

    <!-- 筛选标签 -->
    <section class="mb-4">
      <div class="flex space-x-2 overflow-x-auto scrollbar-hide pb-2">
        <button class="filter-tab active flex-shrink-0 px-4 py-2 bg-primary text-white rounded-full text-sm" data-filter="all">
          全部报告
        </button>
        <button class="filter-tab flex-shrink-0 px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm" data-filter="daily">
          日报分析
        </button>
        <button class="filter-tab flex-shrink-0 px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm" data-filter="weekly">
          周报分析
        </button>
      </div>
    </section>

    <!-- 报告列表 -->
    <section>
      <div id="reports-list" class="space-y-4">
        <!-- 报告卡片 -->
        <div class="report-card bg-white rounded-xl p-4 shadow-sm  cursor-pointer hover:shadow-md transition-shadow"
             data-stock-code="600519" data-stock-name="贵州茅台" data-report-type="daily" data-report-date="2024-01-15">
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center space-x-3">
              <span class="font-medium text-gray-900">贵州茅台</span>
              <span class="text-sm text-gray-500">600519</span>
              <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">日报</span>
            </div>
            <span class="text-xs text-gray-400">30分钟前</span>
          </div>
          <h4 class="font-medium text-gray-900 mb-2">AI日报：白酒龙头估值修复机会显现</h4>
          <p class="text-sm text-gray-600 mb-3">基于技术面和基本面综合分析，茅台当前位置具备较好的投资价值，建议逢低布局</p>
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">买入信号</span>
              <span class="text-xs text-gray-500">目标价 ¥1,750</span>
            </div>
            <div class="flex items-center space-x-3">
              <span class="text-xs text-green-600 font-medium">+12.5%</span>
            </div>
          </div>
        </div>

        <div class="report-card bg-white rounded-xl p-4 shadow-sm cursor-pointer hover:shadow-md transition-shadow"
             data-stock-code="002594" data-stock-name="比亚迪" data-report-type="weekly" data-report-date="2024-01-14">
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center space-x-3">
              <span class="font-medium text-gray-900">比亚迪</span>
              <span class="text-sm text-gray-500">002594</span>
              <span class="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">周报</span>
            </div>
            <span class="text-xs text-gray-400">2小时前</span>
          </div>
          <h4 class="font-medium text-gray-900 mb-2">AI周报：新能源汽车销量超预期增长</h4>
          <p class="text-sm text-gray-600 mb-3">10月销量数据亮眼，技术面突破关键阻力位，短期有望继续上涨</p>
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">加仓信号</span>
              <span class="text-xs text-gray-500">目标价 ¥280</span>
            </div>
            <div class="flex items-center space-x-3">
              <span class="text-xs text-green-600 font-medium">+8.2%</span>
            </div>
          </div>
        </div>

        <div class="report-card bg-white rounded-xl p-4 shadow-sm cursor-pointer hover:shadow-md transition-shadow"
             data-stock-code="300750" data-stock-name="宁德时代" data-report-type="news" data-report-date="2024-01-13">
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center space-x-3">
              <span class="font-medium text-gray-900">宁德时代</span>
              <span class="text-sm text-gray-500">300750</span>
              <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">日报</span>
            </div>
            <span class="text-xs text-gray-400">1小时前</span>
          </div>
          <h4 class="font-medium text-gray-900 mb-2">电池技术突破带来估值重估机会</h4>
          <p class="text-sm text-gray-600 mb-3">新技术发布提升长期竞争力，但短期存在获利回吐压力，建议观望</p>
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <span class="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded">观望信号</span>
              <span class="text-xs text-gray-500">支撑位 ¥180</span>
            </div>
            <div class="flex items-center space-x-3">
              <span class="text-xs text-red-600 font-medium">-2.1%</span>
            </div>
          </div>
        </div>
      </div>
<!-- 自选股日报入口 -->
    <section class="mb-6">
      <div class="bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl p-4 text-white">
        <div class="flex items-center justify-between">
          <div class="flex-1">
            <h3 class="text-lg font-bold mb-1">自选股日报</h3>
            <p class="text-sm opacity-90">基于您的自选股生成的专属投资报告</p>
          </div>
          <button onclick="openWatchlistReport()" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg text-sm font-medium transition-all">
            查看今日报告
            <i class="ri-arrow-right-line ml-1"></i>
          </button>
        </div>
        <div class="grid grid-cols-3 gap-4 mt-4 text-center">
          <div>
            <div class="text-lg font-bold">4</div>
            <div class="text-xs opacity-90">关注股票</div>
          </div>
          <div>
            <div class="text-lg font-bold">+1.2%</div>
            <div class="text-xs opacity-90">平均涨幅</div>
          </div>
          <div>
            <div class="text-lg font-bold">09:00</div>
            <div class="text-xs opacity-90">推送时间</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 自选股周报入口 -->
    <section class="mb-6">
      <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-4 text-white">
        <div class="flex items-center justify-between">
          <div class="flex-1">
            <h3 class="text-lg font-bold mb-1">自选股周报</h3>
            <p class="text-sm opacity-90">深度分析您的自选股本周表现</p>
          </div>
          <button onclick="openWeeklyReport()" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg text-sm font-medium transition-all">
            查看本周报告
            <i class="ri-arrow-right-line ml-1"></i>
          </button>
        </div>
        <div class="grid grid-cols-3 gap-4 mt-4 text-center">
          <div>
            <div class="text-lg font-bold">+2.8%</div>
            <div class="text-xs opacity-90">本周涨幅</div>
          </div>
          <div>
            <div class="text-lg font-bold">3/1</div>
            <div class="text-xs opacity-90">涨跌比例</div>
          </div>
          <div>
            <div class="text-lg font-bold">周五</div>
            <div class="text-xs opacity-90">更新时间</div>
          </div>
        </div>
      </div>
    </section>
      <!-- 加载更多 -->
      <div class="text-center mt-6">
        <button class="px-6 py-2 bg-gray-100 text-gray-600 rounded-full text-sm hover:bg-gray-200 transition-colors">
          <i class="ri-refresh-line mr-1"></i>加载更多报告
        </button>
      </div>
    </section>
  </main>

  <!-- 底部导航栏 -->
  <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
    <div class="grid grid-cols-4 h-16">
      <button onclick="window.location.href='index.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-apps-2-line text-lg text-gray-500"></i>
        </div>
        <span class="text-xs text-gray-500">首页</span>
      </button>
      <button onclick="window.location.href='自选股.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-star-line text-lg text-gray-500"></i>
        </div>
        <span class="text-xs text-gray-500">自选股</span>
      </button>
      <button onclick="window.location.href='reports.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item active">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-file-list-line text-lg text-primary"></i>
        </div>
        <span class="text-xs text-primary">报告中心</span>
      </button>
      <button onclick="window.location.href='我的.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-user-line text-lg text-gray-500"></i>
        </div>
        <span class="text-xs text-gray-500">我的</span>
      </button>
    </div>
  </nav>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 筛选功能
      const filterTabs = document.querySelectorAll('.filter-tab');
      filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
          filterTabs.forEach(t => {
            t.classList.remove('active', 'bg-primary', 'text-white');
            t.classList.add('bg-gray-100', 'text-gray-700');
          });
          this.classList.add('active', 'bg-primary', 'text-white');
          this.classList.remove('bg-gray-100', 'text-gray-700');
          
          // 这里可以添加筛选逻辑
          const filterType = this.getAttribute('data-filter');
          console.log('筛选类型:', filterType);
        });
      });

      // 底部导航栏交互
      const tabItems = document.querySelectorAll('.tab-item');
      tabItems.forEach(item => {
        item.addEventListener('click', function() {
          tabItems.forEach(tab => {
            const icon = tab.querySelector('i');
            const text = tab.querySelector('span');
            icon.classList.remove('text-primary');
            icon.classList.add('text-gray-500');
            text.classList.remove('text-primary');
            text.classList.add('text-gray-500');
          });
          
          const icon = this.querySelector('i');
          const text = this.querySelector('span');
          icon.classList.remove('text-gray-500');
          icon.classList.add('text-primary');
          text.classList.remove('text-gray-500');
          text.classList.add('text-primary');
        });
      });

      // 报告卡片点击事件
      const reportCards = document.querySelectorAll('.report-card');
      reportCards.forEach(card => {
        card.addEventListener('click', function() {
          // 获取报告数据
          const stockCode = this.getAttribute('data-stock-code');
          const stockName = this.getAttribute('data-stock-name');
          const reportType = this.getAttribute('data-report-type');
          const reportDate = this.getAttribute('data-report-date');

          // 跳转到股票日报页面
          const reportUrl = `股票日报.html?code=${stockCode}`;
          window.location.href = reportUrl;
        });
      });

      // 打开自选股日报
      window.openWatchlistReport = function() {
        window.location.href = '自选股日报.html';
      };

      // 打开自选股周报
      window.openWeeklyReport = function() {
        window.location.href = '自选股周报.html';
      };

      // 按股票筛选报告
      window.filterByStock = function() {
        const selector = document.getElementById('stockSelector');
        const selectedStock = selector.value;
        const reportCards = document.querySelectorAll('.report-card');

        reportCards.forEach(card => {
          const stockCode = card.getAttribute('data-stock-code');
          if (selectedStock === 'all' || stockCode === selectedStock) {
            card.style.display = 'block';
          } else {
            card.style.display = 'none';
          }
        });
      };

      // 返回功能
      window.goBack = function() {
        if (window.history.length > 1) {
          window.history.back();
        } else {
          window.location.href = 'index.html';
        }
      };

      // 分享功能
      window.shareApp = function() {
        if (navigator.share) {
          navigator.share({
            title: '金融智能体 - 报告中心',
            text: '查看AI分析报告',
            url: window.location.href
          }).catch(err => {
            console.log('分享失败:', err);
          });
        } else {
          if (navigator.clipboard) {
            navigator.clipboard.writeText(window.location.href).then(() => {
              alert('链接已复制到剪贴板');
            });
          }
        }
      };
    });
  </script>
</body>
</html>
