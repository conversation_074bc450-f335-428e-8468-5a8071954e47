<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI模拟盘 - 智投AI</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
  <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: "#3B82F6",
            secondary: "#F1F5F9",
          },
        },
      },
    };
  </script>
  <style>
    .gradient-bg { 
      background: linear-gradient(135deg, #10B981 0%, #059669 100%);
    }
    .profit-positive { color: #10B981; }
    .profit-negative { color: #EF4444; }
  </style>
</head>
<body class="bg-gray-50 min-h-screen">
  <!-- 顶部导航栏 -->
  <nav class="fixed top-0 w-full bg-white z-50 px-4 py-3 border-b border-gray-100">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <button onclick="goBack()" class="w-8 h-8 flex items-center justify-center mr-3 cursor-pointer">
          <i class="ri-arrow-left-line text-gray-600 text-lg"></i>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">AI模拟盘</h1>
      </div>
      <div class="flex items-center space-x-2">
        <!-- 智能体分析按钮 -->
        <button onclick="openAIChat()" class="w-8 h-8 flex items-center justify-center">
          <i class="ri-robot-line text-gray-600 text-lg"></i>
        </button>
        <!-- 微信小程序分享按钮 -->
        <button onclick="shareApp()" class="w-8 h-8 flex items-center justify-center">
          <div class="flex space-x-0.5">
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
          </div>
        </button>
        <!-- 微信小程序返回主界面按钮 -->
        <button class="w-8 h-8 flex items-center justify-center">
          <div class="w-5 h-5 border-2 border-gray-600 rounded-full flex items-center justify-center">
            <div class="w-2 h-2 bg-gray-600 rounded-full"></div>
          </div>
        </button>
      </div>
    </div>
  </nav>

  <!-- 主要内容区域 -->
  <main class="pt-16 pb-20">
    <!-- 总体表现 -->
    <section class="gradient-bg text-white px-4 py-6">
      <div class="text-center mb-6">
        <h2 class="text-lg font-semibold mb-2">AI模拟盘总资产</h2>
        <div class="text-3xl font-bold mb-2">¥578,900</div>
        <div class="flex items-center justify-center space-x-4">
          <span class="text-sm">总收益 +¥78,900</span>
          <span class="text-sm">收益率 +15.78%</span>
        </div>
      </div>

      <div class="grid grid-cols-3 gap-4 text-center">
        <div>
          <div class="text-lg font-bold">¥500,000</div>
          <div class="text-xs opacity-90">初始资金</div>
        </div>
        <div>
          <div class="text-lg font-bold">45天</div>
          <div class="text-xs opacity-90">运行天数</div>
        </div>
        <div>
          <div class="text-lg font-bold">4只</div>
          <div class="text-xs opacity-90">持仓股票</div>
        </div>
      </div>
    </section>

    <!-- 收益曲线图 -->
    <section class="bg-white px-4 py-6 border-b border-gray-100">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">收益曲线</h3>
      <div id="profit-chart" style="height: 200px;"></div>
    </section>

    <!-- 持仓股票 -->
    <section class="bg-white px-4 py-6 border-b border-gray-100">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">当前持仓</h3>
        <span class="text-sm text-gray-500">4只自选股</span>
      </div>
      
      <div class="space-y-3">
        <!-- 仓位最高股票 -->
        <div class="stock-card p-4 bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg border border-orange-200 cursor-pointer hover:shadow-md transition-shadow"
             data-stock-code="600519" data-stock-name="贵州茅台">
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center space-x-3">
              <span class="font-medium text-gray-900">贵州茅台</span>
              <span class="text-sm text-gray-500">600519</span>
              <span class="text-xs bg-orange-500 text-white px-2 py-1 rounded-full">仓位最高</span>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900">100股</div>
              <div class="text-xs profit-positive">+12.5%</div>
            </div>
          </div>
          <div class="flex items-center justify-between text-sm">
            <span class="text-gray-600">成本价：¥1,600.00</span>
            <span class="text-gray-600">现价：¥1,678.50</span>
          </div>
          <div class="flex items-center justify-between text-sm mt-1">
            <span class="text-gray-600">持仓市值：¥167,850</span>
            <span class="profit-positive font-medium">+¥7,850</span>
          </div>
          <div class="flex items-center justify-between text-sm mt-2 pt-2 border-t border-orange-200">
            <span class="text-gray-600">仓位比例：29.0%</span>
            <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">AI建议：持有</span>
          </div>
        </div>

        <div class="stock-card p-4 bg-gray-50 rounded-lg cursor-pointer hover:shadow-md transition-shadow"
             data-stock-code="002594" data-stock-name="比亚迪">
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center space-x-3">
              <span class="font-medium text-gray-900">比亚迪</span>
              <span class="text-sm text-gray-500">002594</span>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900">200股</div>
              <div class="text-xs profit-positive">+8.2%</div>
            </div>
          </div>
          <div class="flex items-center justify-between text-sm">
            <span class="text-gray-600">成本价：¥230.00</span>
            <span class="text-gray-600">现价：¥245.80</span>
          </div>
          <div class="flex items-center justify-between text-sm mt-1">
            <span class="text-gray-600">持仓市值：¥147,480</span>
            <span class="profit-positive font-medium">+¥9,480</span>
          </div>
          <div class="flex items-center justify-between text-sm mt-2 pt-2 border-t border-gray-200">
            <span class="text-gray-600">仓位比例：25.5%</span>
            <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">AI建议：加仓</span>
          </div>
        </div>

        <div class="stock-card p-4 bg-gray-50 rounded-lg cursor-pointer hover:shadow-md transition-shadow"
             data-stock-code="300750" data-stock-name="宁德时代">
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center space-x-3">
              <span class="font-medium text-gray-900">宁德时代</span>
              <span class="text-sm text-gray-500">300750</span>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900">150股</div>
              <div class="text-xs profit-negative">-2.1%</div>
            </div>
          </div>
          <div class="flex items-center justify-between text-sm">
            <span class="text-gray-600">成本价：¥190.00</span>
            <span class="text-gray-600">现价：¥185.20</span>
          </div>
          <div class="flex items-center justify-between text-sm mt-1">
            <span class="text-gray-600">持仓市值：¥92,600</span>
            <span class="profit-negative font-medium">-¥2,400</span>
          </div>
          <div class="flex items-center justify-between text-sm mt-2 pt-2 border-t border-gray-200">
            <span class="text-gray-600">仓位比例：16.0%</span>
            <span class="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded">AI建议：观望</span>
          </div>
        </div>

        <div class="stock-card p-4 bg-gray-50 rounded-lg cursor-pointer hover:shadow-md transition-shadow"
             data-stock-code="601318" data-stock-name="中国平安">
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center space-x-3">
              <span class="font-medium text-gray-900">中国平安</span>
              <span class="text-sm text-gray-500">601318</span>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900">3000股</div>
              <div class="text-xs profit-positive">+5.6%</div>
            </div>
          </div>
          <div class="flex items-center justify-between text-sm">
            <span class="text-gray-600">成本价：¥40.20</span>
            <span class="text-gray-600">现价：¥42.50</span>
          </div>
          <div class="flex items-center justify-between text-sm mt-1">
            <span class="text-gray-600">持仓市值：¥127,500</span>
            <span class="profit-positive font-medium">+¥6,900</span>
          </div>
          <div class="flex items-center justify-between text-sm mt-2 pt-2 border-t border-gray-200">
            <span class="text-gray-600">仓位比例：22.0%</span>
            <span class="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">AI建议：减仓</span>
          </div>
        </div>
      </div>
    </section>

    <!-- AI策略表现 -->
    <section class="bg-white px-4 py-6 border-b border-gray-100">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">AI策略表现</h3>

      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="text-center p-4 bg-green-50 rounded-lg">
          <div class="text-xl font-bold text-green-600">23次</div>
          <div class="text-sm text-gray-600">当日交易次数</div>
        </div>
        <div class="text-center p-4 bg-blue-50 rounded-lg">
          <div class="text-xl font-bold text-blue-600">12天</div>
          <div class="text-sm text-gray-600">平均持仓天数</div>
        </div>
      </div>

      <div class="space-y-3 mb-4">
        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <span class="text-sm text-gray-700">最大回撤率</span>
          <span class="text-sm font-medium text-red-600">-2.5%</span>
        </div>
        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <span class="text-sm text-gray-700">最大单笔收益</span>
          <span class="text-sm font-medium profit-positive">+¥9,480 (+6.9%)</span>
        </div>
        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <span class="text-sm text-gray-700">最大单笔亏损</span>
          <span class="text-sm font-medium profit-negative">-¥3,200 (-1.1%)</span>
        </div>
      </div>

      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 class="font-medium text-blue-800 mb-2 flex items-center">
          <i class="ri-robot-line mr-2"></i>AI交易策略
        </h4>
        <div class="text-sm text-blue-700 space-y-1">
          <p>• 基于您的自选股进行智能分析和交易</p>
          <p>• 采用中长期投资策略，平均持仓12天</p>
          <p>• 严格风险控制，单笔交易不超过总资产5%</p>
          <p>• 实时监控市场变化，动态调整仓位配置</p>
        </div>
      </div>
    </section>

    <!-- 交易记录 -->
    <section class="bg-white px-4 py-6 border-b border-gray-100">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">最近交易</h3>
        <button class="text-sm text-primary">查看全部</button>
      </div>
      
      <div class="space-y-3">
        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div class="flex-1">
            <div class="flex items-center space-x-2 mb-1">
              <span class="text-sm font-medium text-gray-900">买入 贵州茅台</span>
              <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">AI建议</span>
            </div>
            <div class="text-xs text-gray-500">2024-01-15 09:30 · 100股 · ¥1,600.00</div>
          </div>
          <div class="text-right">
            <div class="text-sm font-medium profit-positive">+¥7,850</div>
            <div class="text-xs text-gray-500">+4.9%</div>
          </div>
        </div>

        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div class="flex-1">
            <div class="flex items-center space-x-2 mb-1">
              <span class="text-sm font-medium text-gray-900">加仓 比亚迪</span>
              <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">AI建议</span>
            </div>
            <div class="text-xs text-gray-500">2024-01-12 14:20 · 300股 · ¥230.00</div>
          </div>
          <div class="text-right">
            <div class="text-sm font-medium profit-positive">+¥4,740</div>
            <div class="text-xs text-gray-500">+6.9%</div>
          </div>
        </div>

        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div class="flex-1">
            <div class="flex items-center space-x-2 mb-1">
              <span class="text-sm font-medium text-gray-900">买入 中国平安</span>
              <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">AI建议</span>
            </div>
            <div class="text-xs text-gray-500">2024-01-08 11:20 · 3000股 · ¥40.20</div>
          </div>
          <div class="text-right">
            <div class="text-sm font-medium profit-positive">+¥6,900</div>
            <div class="text-xs text-gray-500">+5.7%</div>
          </div>
        </div>

        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div class="flex-1">
            <div class="flex items-center space-x-2 mb-1">
              <span class="text-sm font-medium text-gray-900">减仓 宁德时代</span>
              <span class="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">AI建议</span>
            </div>
            <div class="text-xs text-gray-500">2024-01-05 15:45 · 200股 · ¥195.00</div>
          </div>
          <div class="text-right">
            <div class="text-sm font-medium profit-negative">-¥1,960</div>
            <div class="text-xs text-gray-500">-2.5%</div>
          </div>
        </div>
      </div>
    </section>


  </main>

  <!-- 底部导航栏 -->
  <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
    <div class="grid grid-cols-4 h-16">
      <button onclick="window.location.href='index.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-apps-2-line text-lg text-gray-500"></i>
        </div>
        <span class="text-xs text-gray-500">首页</span>
      </button>
      <button onclick="window.location.href='自选股.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-star-line text-lg text-gray-500"></i>
        </div>
        <span class="text-xs text-gray-500">自选股</span>
      </button>
      <button onclick="window.location.href='reports.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-file-list-line text-lg text-gray-500"></i>
        </div>
        <span class="text-xs text-gray-500">报告中心</span>
      </button>
      <button onclick="window.location.href='我的.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-user-line text-lg text-gray-500"></i>
        </div>
        <span class="text-xs text-gray-500">我的</span>
      </button>
    </div>
  </nav>

  <script>
    document.addEventListener("DOMContentLoaded", function () {
      // 初始化收益曲线图
      initProfitChart();

      // 底部导航栏交互
      const tabItems = document.querySelectorAll(".tab-item");
      tabItems.forEach((item) => {
        item.addEventListener("click", function () {
          tabItems.forEach((tab) => {
            const icon = tab.querySelector("i");
            const text = tab.querySelector("span");
            icon.classList.remove("text-primary");
            icon.classList.add("text-gray-500");
            text.classList.remove("text-primary", "font-medium");
            text.classList.add("text-gray-500");
            tab.classList.remove("active");
          });
          const icon = this.querySelector("i");
          const text = this.querySelector("span");
          icon.classList.remove("text-gray-500");
          icon.classList.add("text-primary");
          text.classList.remove("text-gray-500");
          text.classList.add("text-primary", "font-medium");
          this.classList.add("active");
        });
      });
    });

    // 初始化收益曲线图
    function initProfitChart() {
      const chartDom = document.getElementById('profit-chart');
      const myChart = echarts.init(chartDom);

      // 生成模拟收益数据
      const dates = [];
      const profits = [];
      let baseProfit = 0;
      
      for (let i = 0; i < 45; i++) {
        const date = new Date();
        date.setDate(date.getDate() - (44 - i));
        dates.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }));

        baseProfit += (Math.random() - 0.35) * 2500; // 模拟收益波动，适配50万资金
        profits.push(Math.max(-25000, Math.min(100000, baseProfit)));
      }
      
      const option = {
        grid: {
          left: '3%',
          right: '3%',
          bottom: '3%',
          top: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dates,
          axisLine: { lineStyle: { color: '#E5E7EB' } },
          axisLabel: { color: '#6B7280', fontSize: 10 },
          axisTick: { show: false }
        },
        yAxis: {
          type: 'value',
          axisLine: { show: false },
          axisLabel: { color: '#6B7280', fontSize: 10 },
          splitLine: { lineStyle: { color: '#F3F4F6' } }
        },
        series: [
          {
            data: profits,
            type: 'line',
            smooth: true,
            lineStyle: {
              color: '#10B981',
              width: 2
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(16, 185, 129, 0.3)' },
                  { offset: 1, color: 'rgba(16, 185, 129, 0.05)' }
                ]
              }
            },
            symbol: 'none'
          }
        ],
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            const value = params[0].data;
            const sign = value >= 0 ? '+' : '';
            return `${params[0].axisValue}<br/>收益：${sign}¥${value.toFixed(0)}`;
          }
        }
      };

      myChart.setOption(option);
      
      // 响应式调整
      window.addEventListener('resize', function() {
        myChart.resize();
      });
    }

    // 返回功能
    function goBack() {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        window.location.href = 'index.html';
      }
    }

    // 分享功能
    function shareApp() {
      if (navigator.share) {
        navigator.share({
          title: '金融智能体 - AI模拟盘',
          text: '查看AI模拟盘表现',
          url: window.location.href
        }).catch(err => {
          console.log('分享失败:', err);
        });
      } else {
        if (navigator.clipboard) {
          navigator.clipboard.writeText(window.location.href).then(() => {
            alert('链接已复制到剪贴板');
          });
        }
      }
    }

    // 打开AI智能体分析
    function openAIChat() {
      window.location.href = 'AI选股.html#ai-chat';
    }

    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 股票卡片点击事件
      const stockCards = document.querySelectorAll('.stock-card');
      stockCards.forEach(card => {
        card.addEventListener('click', function() {
          const stockCode = this.getAttribute('data-stock-code');
          const stockName = this.getAttribute('data-stock-name');

          // 跳转到个股模拟盘页面
          const url = `个股模拟盘.html?code=${stockCode}&name=${encodeURIComponent(stockName)}`;
          window.location.href = url;
        });
      });
    });
  </script>
</body>
</html>
