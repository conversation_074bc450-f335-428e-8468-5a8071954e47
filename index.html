<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>股票资讯小程序</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link
    href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
    rel="stylesheet"
  />
  <link
    rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
  />
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: "#3B82F6",
            secondary: "#F1F5F9",
          },
          borderRadius: {
            none: "0px",
            sm: "4px",
            DEFAULT: "8px",
            md: "12px",
            lg: "16px",
            xl: "20px",
            "2xl": "24px",
            "3xl": "32px",
            full: "9999px",
            button: "8px",
          },
        },
      },
    };
  </script>
  <style>
    :where([class^="ri-"])::before {
    content: "\f3c2";
    }
    .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
    }
    .scrollbar-hide::-webkit-scrollbar {
    display: none;
    }
    .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    }
    .gradient-bg {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  </style>
</head>
<body class="bg-gray-50 min-h-screen">
  <!-- 顶部导航栏 -->
  <nav class="fixed top-0 w-full bg-white z-50 px-4 py-3 border-b border-gray-100">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <h1 class="text-xl font-bold text-gray-900">金融智能体</h1>
      </div>
      <div class="flex items-center space-x-2">
        <!-- 搜索按钮 -->
        <button onclick="openSearch()" class="w-8 h-8 flex items-center justify-center">
          <i class="ri-search-line text-gray-600 text-lg"></i>
        </button>
        <!-- 消息中心按钮 -->
        <button onclick="openMessageCenter()" class="w-8 h-8 flex items-center justify-center relative">
          <i class="ri-notification-line text-gray-600 text-lg"></i>
          <!-- 未读消息提示点 -->
          <div class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
        </button>
        <!-- 微信小程序分享按钮 -->
        <button onclick="shareApp()" class="w-8 h-8 flex items-center justify-center">
          <div class="flex space-x-0.5">
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
          </div>
        </button>
        <!-- 微信小程序返回主界面按钮 -->
        <button class="w-8 h-8 flex items-center justify-center">
          <div class="w-5 h-5 border-2 border-gray-600 rounded-full flex items-center justify-center">
            <div class="w-2 h-2 bg-gray-600 rounded-full"></div>
          </div>
        </button>
      </div>
    </div>
  </nav>

  <!-- 主要内容区域 -->
  <main class="pt-16 pb-20 px-4">

    <!-- AI自选股模拟盘 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">自选股模拟盘</h3>
          <a href="模拟盘.html" class="text-sm text-primary">查看详情</a>
        </div>

        <div class="grid grid-cols-3 gap-4 mb-4">
          <div class="text-center">
            <div class="text-lg font-bold text-gray-900">¥578,900</div>
            <div class="text-xs text-gray-500">总资产</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-bold text-green-600">+15.78%</div>
            <div class="text-xs text-gray-500">总收益率</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-bold text-green-600">+¥8,500</div>
            <div class="text-xs text-gray-500">总盈亏</div>
          </div>
        </div>

        <!-- 持仓股票简览 -->
        <div class="space-y-2">
          <div class="flex items-center justify-between text-sm">
            <div class="flex items-center">
              <span class="font-medium text-gray-900">贵州茅台</span>
              <span class="text-gray-500 ml-2">600519</span>
            </div>
            <div class="text-right">
              <div class="text-green-600 font-medium">+4.9%</div>
            </div>
          </div>
          <div class="flex items-center justify-between text-sm">
            <div class="flex items-center">
              <span class="font-medium text-gray-900">比亚迪</span>
              <span class="text-gray-500 ml-2">002594</span>
            </div>
            <div class="text-right">
              <div class="text-green-600 font-medium">+6.9%</div>
            </div>
          </div>
          <div class="flex items-center justify-between text-sm">
            <div class="flex items-center">
              <span class="font-medium text-gray-900">宁德时代</span>
              <span class="text-gray-500 ml-2">300750</span>
            </div>
            <div class="text-right">
              <div class="text-red-600 font-medium">-2.5%</div>
            </div>
          </div>
          <div class="flex items-center justify-between text-sm">
            <div class="flex items-center">
              <span class="font-medium text-gray-900">中国平安</span>
              <span class="text-gray-500 ml-2">601318</span>
            </div>
            <div class="text-right">
              <div class="text-green-600 font-medium">+5.7%</div>
            </div>
          </div>
        </div>
      </div>
    </section>


    <!-- AI分析报告 -->
    <section class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-gray-900">分析报告</h2>
        <button onclick="window.location.href='reports.html'" class="text-sm text-primary cursor-pointer">查看全部</button>
      </div>

      <!-- 自选股分析报告 - 仓位最高 -->
      <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 mb-4 border border-blue-100">
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center space-x-2">
            <i class="ri-robot-line text-primary text-lg"></i>
            <span class="font-semibold text-gray-900">自选股分析</span>
            <span class="text-xs bg-primary text-white px-2 py-1 rounded-full">仓位最高</span>
          </div>
          <div class="flex items-center space-x-2">
            <button class="text-xs text-primary cursor-pointer" onclick="toggleRecommendationMode()">
              <i class="ri-settings-3-line mr-1"></i>设置
            </button>
            <span class="text-xs text-gray-500">刚刚更新</span>
          </div>
        </div>

        <div class="bg-white rounded-lg p-3 cursor-pointer" onclick="window.location.href='股票日报.html?code=600519'">
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center space-x-3">
              <span class="font-medium text-gray-900">贵州茅台</span>
              <span class="text-sm text-gray-500">600519</span>
              <span class="text-sm text-red-500 font-medium">+2.35%</span>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900">¥1,678.50</div>
              <div class="text-xs text-green-600">模拟盈利 +12.5%</div>
            </div>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">AI建议：持有</span>
            <div class="flex items-center space-x-2">
              <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">智投AI</span>
            </div>
          </div>
        </div>

        <!-- 第二个自选股分析 - 比亚迪 -->
        <div class="bg-white rounded-lg p-3 cursor-pointer mt-3" onclick="window.location.href='股票日报.html?code=002594'">
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center space-x-3">
              <span class="font-medium text-gray-900">比亚迪</span>
              <span class="text-sm text-gray-500">002594</span>
              <span class="text-sm text-red-500 font-medium">*****%</span>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900">¥245.80</div>
              <div class="text-xs text-green-600">模拟盈利 +6.9%</div>
            </div>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">AI建议：加仓</span>
            <div class="flex items-center space-x-2">
              <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">智投AI</span>
            </div>
          </div>
        </div>

        <!-- 推荐模式选择弹窗（隐藏） -->
        <div id="recommendation-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <div class="bg-white rounded-xl p-6 mx-4 w-full max-w-sm">
            <h3 class="font-semibold text-gray-900 mb-4">选择推荐模式</h3>
            <div class="space-y-3">
              <label class="flex items-center">
                <input type="radio" name="recommend-mode" value="highest" checked class="mr-3">
                <span class="text-sm">仓位最高股票（默认）</span>
              </label>
              <label class="flex items-center">
                <input type="radio" name="recommend-mode" value="custom" class="mr-3">
                <span class="text-sm">用户指定股票</span>
              </label>
            </div>
            <div class="flex space-x-3 mt-6">
              <button onclick="closeRecommendationModal()" class="flex-1 py-2 border border-gray-300 rounded-lg text-sm">取消</button>
              <button onclick="saveRecommendationMode()" class="flex-1 py-2 bg-primary text-white rounded-lg text-sm">确定</button>
            </div>
          </div>
        </div>
      </div>

    </section>

    <!-- AI今日推荐 -->
    <section class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-gray-900">今日推荐</h2>
        <button onclick="window.location.href='AI选股.html'" class="text-sm text-primary cursor-pointer">查看更多</button>
      </div>

      <div class="space-y-3">
        <!-- 短线推荐 -->
        <div class="bg-white rounded-xl p-4 shadow-sm">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
              <span class="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">短线</span>
              <span class="font-medium text-gray-900">中国平安</span>
              <span class="text-sm text-gray-500">601318</span>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900">¥45.67</div>
              <div class="text-xs text-green-600">+1.23%</div>
            </div>
          </div>
          <div class="text-xs text-gray-600 mb-2">
            AI分析：技术面突破，成交量放大，短期看涨
          </div>
          <div class="flex items-center justify-between">
            <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">AI建议：买入</span>
            <button onclick="addToWatchlist('601318', '中国平安')" class="text-xs text-primary">+加自选</button>
          </div>
        </div>

        <!-- 中长线推荐 -->
        <div class="bg-white rounded-xl p-4 shadow-sm">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
              <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">中长线</span>
              <span class="font-medium text-gray-900">招商银行</span>
              <span class="text-sm text-gray-500">600036</span>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900">¥32.45</div>
              <div class="text-xs text-red-600">-0.56%</div>
            </div>
          </div>
          <div class="text-xs text-gray-600 mb-2">
            AI分析：基本面稳健，估值合理，适合长期持有
          </div>
          <div class="flex items-center justify-between">
            <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">AI建议：买入</span>
            <button onclick="addToWatchlist('600036', '招商银行')" class="text-xs text-primary">+加自选</button>
          </div>
        </div>
      </div>
    </section>

    <!-- 最新资讯 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold text-gray-900">最新资讯</h2>
          <div class="flex items-center space-x-2">
            <!-- 自选股票下拉选择 -->
            <div class="relative">
              <button onclick="toggleStockSelector()" class="flex items-center space-x-1 text-sm text-primary bg-blue-50 px-3 py-1 rounded-full">
                <span id="selected-stock-name">自选</span>
                <i class="ri-arrow-down-s-line text-xs"></i>
              </button>
              <!-- 下拉菜单 -->
              <div id="stock-selector" class="hidden absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-[120px]">
                <div class="py-1">
                  <button onclick="selectStock('all', '要闻')" class="w-full text-left px-3 py-2 text-sm hover:bg-gray-50">要闻</button>
                  <button onclick="selectStock('600519', '贵州茅台')" class="w-full text-left px-3 py-2 text-sm hover:bg-gray-50">贵州茅台</button>
                  <button onclick="selectStock('002594', '比亚迪')" class="w-full text-left px-3 py-2 text-sm hover:bg-gray-50">比亚迪</button>
                  <button onclick="selectStock('300750', '宁德时代')" class="w-full text-left px-3 py-2 text-sm hover:bg-gray-50">宁德时代</button>
                </div>
              </div>
            </div>
            <button onclick="window.location.href='news.html'" class="text-sm text-primary cursor-pointer flex items-center">
              查看更多 <i class="ri-arrow-right-s-line ml-1"></i>
            </button>
          </div>
        </div>

        <div class="space-y-3" id="news-content">
          <div class="border-l-4 border-red-500 pl-3 py-2 cursor-pointer hover:bg-gray-50 rounded-r" onclick="openNews('news1')">
            <h3 class="text-sm font-medium text-gray-900 mb-1">央行降准释放流动性，银行股集体上涨</h3>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">财经新闻 · 2小时前</span>
              <span class="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">重要</span>
            </div>
          </div>

          <div class="border-l-4 border-blue-500 pl-3 py-2 cursor-pointer hover:bg-gray-50 rounded-r" onclick="openNews('news3')">
            <h3 class="text-sm font-medium text-gray-900 mb-1">新能源汽车销量创新高，相关概念股活跃</h3>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">行业资讯 · 4小时前</span>
              <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">关注</span>
            </div>
          </div>

          <div class="border-l-4 border-green-500 pl-3 py-2 cursor-pointer hover:bg-gray-50 rounded-r" onclick="openNews('news4')">
            <h3 class="text-sm font-medium text-gray-900 mb-1">A股三大指数集体收涨，创业板指涨超1.5%</h3>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">市场快讯 · 6小时前</span>
              <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">利好</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 股票搜索输入 -->
    <section class="mb-6">
      <div class="gradient-bg rounded-xl p-6 text-white text-center">
        <h2 class="text-lg font-semibold mb-4">输入股票代码获取AI分析</h2>
        <div class="relative max-w-md mx-auto">
          <input
            type="text"
            id="stockInput"
            placeholder="输入股票代码，如：600519"
            class="w-full px-4 py-3 rounded-lg text-gray-900 text-center text-lg font-medium"
          />
          <button
            onclick="analyzeStock()"
            class="mt-3 w-full bg-white text-primary py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            <i class="ri-robot-line mr-2"></i>开始AI分析
          </button>
        </div>
      </div>
    </section>
    <!-- 编辑模块 -->
    <section class="mb-6">
      <div class="bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-xl p-6 text-white relative overflow-hidden">
        <!-- 背景装饰 -->
        <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
        <div class="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>

        <div class="relative z-10">
          <div class="flex items-center justify-between mb-4">
            <div>
              <h3 class="text-lg font-bold mb-1">个性化定制</h3>
              <p class="text-white/80 text-sm">打造专属于您的投资界面</p>
            </div>
            <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
              <i class="ri-palette-line text-xl"></i>
            </div>
          </div>

          <div class="grid grid-cols-3 gap-3 mb-4">
            <div class="bg-white/20 rounded-lg p-2 text-center backdrop-blur-sm">
              <i class="ri-layout-grid-line text-lg mb-1"></i>
              <div class="text-xs">布局</div>
            </div>
            <div class="bg-white/20 rounded-lg p-2 text-center backdrop-blur-sm">
              <i class="ri-color-filter-line text-lg mb-1"></i>
              <div class="text-xs">主题</div>
            </div>
            <div class="bg-white/20 rounded-lg p-2 text-center backdrop-blur-sm">
              <i class="ri-settings-3-line text-lg mb-1"></i>
              <div class="text-xs">功能</div>
            </div>
          </div>

          <button onclick="window.location.href='编辑模块.html'" class="w-full bg-white text-purple-600 font-semibold py-3 rounded-lg hover:bg-white/90 transition-colors flex items-center justify-center space-x-2">
            <i class="ri-edit-line"></i>
            <span>开始定制</span>
          </button>
        </div>
      </div>
    </section>





    <!-- 分析结果区域 -->
    <section id="analysisSection" class="hidden">
      <!-- 股票基本信息 -->
      <div id="stockInfo" class="bg-white rounded-xl p-4 mb-4 shadow-sm">
        <!-- 动态生成股票信息 -->
      </div>

      <!-- 功能选择 -->
      <div class="grid grid-cols-2 gap-3 mb-6">
        <button onclick="showRealtimeNews()" class="bg-white p-4 rounded-xl shadow-sm text-center hover:shadow-md transition-shadow">
          <i class="ri-news-line text-2xl text-blue-500 mb-2"></i>
          <div class="text-sm font-medium">实时资讯</div>
        </button>
        <button onclick="showOpeningAnalysis()" class="bg-white p-4 rounded-xl shadow-sm text-center hover:shadow-md transition-shadow">
          <i class="ri-sun-line text-2xl text-orange-500 mb-2"></i>
          <div class="text-sm font-medium">开盘分析</div>
        </button>
        <button id="closingAnalysisBtn" onclick="showClosingAnalysis()" class="bg-white p-4 rounded-xl shadow-sm text-center hover:shadow-md transition-shadow">
          <i class="ri-moon-line text-2xl text-indigo-500 mb-2"></i>
          <div class="text-sm font-medium">收盘总结</div>
        </button>
        <button onclick="showWeeklyReport()" class="bg-white p-4 rounded-xl shadow-sm text-center hover:shadow-md transition-shadow">
          <i class="ri-calendar-line text-2xl text-purple-500 mb-2"></i>
          <div class="text-sm font-medium">周报分析</div>
        </button>
      </div>

      <!-- 内容显示区域 -->
      <div id="contentArea" class="bg-white rounded-xl p-4 shadow-sm mb-6">
        <div class="text-center text-gray-500 py-8">
          <i class="ri-cursor-line text-3xl mb-2"></i>
          <p>请选择上方功能查看详细分析</p>
        </div>
      </div>
    </section>
  </main>

  <!-- 底部导航栏 -->
  <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
    <div class="grid grid-cols-4 h-16">
      <button onclick="window.location.href='index.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item active">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-apps-2-line text-lg text-primary"></i>
        </div>
        <span class="text-xs text-primary">首页</span>
      </button>
      <button onclick="window.location.href='自选股.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-star-line text-lg text-gray-500"></i>
        </div>
        <span class="text-xs text-gray-500">自选股</span>
      </button>
      <button onclick="window.location.href='reports.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-file-list-line text-lg text-gray-500"></i>
        </div>
        <span class="text-xs text-gray-500">报告中心</span>
      </button>
      <button onclick="window.location.href='我的.html'" class="flex flex-col items-center justify-center space-y-1 cursor-pointer tab-item">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-user-line text-lg text-gray-500"></i>
        </div>
        <span class="text-xs text-gray-500">我的</span>
      </button>
    </div>
  </nav>

  <script>
    let currentStock = null;

    // 股票数据库（模拟）
    const stockDatabase = {
      '600519': {
        name: '贵州茅台',
        price: '1678.50',
        change: '+2.35%',
        market: 'A股',
        industry: '白酒'
      },
      '000858': {
        name: '五粮液',
        price: '158.20',
        change: '*****%',
        market: 'A股',
        industry: '白酒'
      },
      '002594': {
        name: '比亚迪',
        price: '245.80',
        change: '*****%',
        market: 'A股',
        industry: '新能源汽车'
      },
      '300750': {
        name: '宁德时代',
        price: '185.20',
        change: '-0.65%',
        market: 'A股',
        industry: '电池'
      }
    };



    // 分析股票
    function analyzeStock() {
      const stockCode = document.getElementById('stockInput').value.trim();

      if (!stockCode) {
        // 如果没有输入，直接跳转到搜索分析页面
        window.location.href = '股票搜索分析.html';
        return;
      }

      // 带着股票代码跳转到股票日报页面
      window.location.href = `股票日报.html?code=${encodeURIComponent(stockCode)}`;
    }











    // 获取当前日期和时间信息
    function getCurrentDateInfo() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = now.getHours();
      const minutes = String(now.getMinutes()).padStart(2, '0');

      const dateStr = `${year}-${month}-${day}`;
      const timeStr = `${hours}:${minutes}`;

      // 判断是否为交易时间
      const isWeekday = now.getDay() >= 1 && now.getDay() <= 5; // 周一到周五
      const isMarketOpen = hours >= 9 && hours < 15; // 9:00-15:00为交易时间
      const isAfterMarket = hours >= 15; // 15:00后为收盘后

      return {
        dateStr,
        timeStr,
        isWeekday,
        isMarketOpen,
        isAfterMarket,
        canViewClosing: isWeekday && isAfterMarket
      };
    }

    // 显示股票基本信息
    function displayStockInfo() {
      const stockInfoDiv = document.getElementById('stockInfo');
      const changeColor = currentStock.change.startsWith('+') ? 'text-red-500' : 'text-green-500';
      const dateInfo = getCurrentDateInfo();

      stockInfoDiv.innerHTML = `
        <div class="flex items-center justify-between mb-3">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">${currentStock.name}</h3>
            <p class="text-sm text-gray-500">${currentStock.code} · ${currentStock.market} · ${currentStock.industry}</p>
          </div>
          <div class="text-right">
            <div class="text-xl font-bold text-gray-900">¥${currentStock.price}</div>
            <div class="text-sm ${changeColor} font-medium">${currentStock.change}</div>
          </div>
        </div>
        <div class="flex items-center justify-between pt-3 border-t border-gray-100">
          <div class="flex items-center space-x-4">
            <div class="flex items-center text-sm text-gray-600">
              <i class="ri-calendar-line mr-1"></i>
              <span>${dateInfo.dateStr}</span>
            </div>
            <div class="flex items-center text-sm text-gray-600">
              <i class="ri-time-line mr-1"></i>
              <span>${dateInfo.timeStr}</span>
            </div>
            <span class="text-xs px-2 py-1 rounded ${dateInfo.isMarketOpen ? 'bg-green-100 text-green-700' : dateInfo.canViewClosing ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'}">
              ${dateInfo.isMarketOpen ? '交易中' : dateInfo.canViewClosing ? '已收盘' : '休市'}
            </span>
          </div>
          <div class="flex items-center space-x-3">
            <button id="addToWatchlistBtn" onclick="toggleWatchlist('${currentStock.code}')" class="flex items-center justify-center px-4 py-2 text-sm rounded-lg transition-all duration-200 min-w-[80px]">
              <i id="watchlistIcon" class="mr-1.5"></i>
              <span id="watchlistText"></span>
            </button>

          </div>
        </div>
      `;

      // 更新收盘分析按钮状态
      updateClosingAnalysisButton(dateInfo);

      // 更新自选股按钮状态
      updateWatchlistButton(currentStock.code);
    }

    // 更新收盘分析按钮状态
    function updateClosingAnalysisButton(dateInfo) {
      const closingBtn = document.getElementById('closingAnalysisBtn');
      if (!closingBtn) return;

      if (!dateInfo.canViewClosing) {
        closingBtn.classList.add('opacity-50', 'cursor-not-allowed');
        closingBtn.classList.remove('hover:shadow-md');
        closingBtn.onclick = function() {
          showClosingUnavailable();
        };

        // 更新按钮文字
        const btnText = closingBtn.querySelector('.text-sm');
        if (btnText) {
          btnText.innerHTML = '收盘总结<br><span class="text-xs text-gray-400">收盘后可查看</span>';
        }
      } else {
        closingBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        closingBtn.classList.add('hover:shadow-md');
        closingBtn.onclick = function() {
          showClosingAnalysis();
        };

        // 恢复按钮文字
        const btnText = closingBtn.querySelector('.text-sm');
        if (btnText) {
          btnText.innerHTML = '收盘总结';
        }
      }
    }

    // 分析特定股票（从推荐卡片点击）
    function analyzeSpecificStock(stockCode) {
      document.getElementById('stockInput').value = stockCode;
      analyzeStock();
    }

    // 显示报告详情
    function showReportDetail(stockName, stockCode) {
      alert(`查看${stockName}(${stockCode})的详细分析报告`);
      // 这里可以跳转到详细报告页面或显示详细内容
    }

    // 推荐模式相关函数
    function toggleRecommendationMode() {
      document.getElementById('recommendation-modal').classList.remove('hidden');
    }

    function closeRecommendationModal() {
      document.getElementById('recommendation-modal').classList.add('hidden');
    }

    function saveRecommendationMode() {
      const selectedMode = document.querySelector('input[name="recommend-mode"]:checked').value;
      console.log('选择的推荐模式:', selectedMode);
      closeRecommendationModal();
      // 这里可以保存用户选择并更新推荐内容
    }

    // 今日投资思考相关功能
    const dailyQuestions = [
      "在当前市场环境下，您认为哪个行业板块最具投资价值？为什么？",
      "如何在震荡市场中保持理性投资心态？",
      "您如何看待当前的市场估值水平？",
      "在投资组合中，您会如何配置不同风险等级的资产？",
      "面对市场不确定性，您的风险管理策略是什么？"
    ];

    function refreshQuestion() {
      const questionElement = document.getElementById('daily-question');
      const randomIndex = Math.floor(Math.random() * dailyQuestions.length);
      questionElement.textContent = dailyQuestions[randomIndex];

      // 添加刷新动画
      questionElement.style.opacity = '0.5';
      setTimeout(() => {
        questionElement.style.opacity = '1';
      }, 200);
    }

    function showQuestionDetail() {
      alert('讨论功能开发中，敬请期待！');
    }

    // 股票选择器相关功能
    function toggleStockSelector() {
      const selector = document.getElementById('stock-selector');
      selector.classList.toggle('hidden');
    }

    function selectStock(stockCode, stockName) {
      const selectedNameElement = document.getElementById('selected-stock-name');
      const newsContent = document.getElementById('news-content');

      selectedNameElement.textContent = stockName;

      // 根据选择的股票更新新闻内容
      updateNewsContent(stockCode, stockName);

      // 关闭下拉菜单
      document.getElementById('stock-selector').classList.add('hidden');
    }

    function updateNewsContent(stockCode, stockName) {
      const newsContent = document.getElementById('news-content');

      if (stockCode === 'all') {
        // 显示要闻
        newsContent.innerHTML = `
          <div class="border-l-4 border-red-500 pl-3 py-2 cursor-pointer hover:bg-gray-50 rounded-r" onclick="openNews('news1')">
            <h3 class="text-sm font-medium text-gray-900 mb-1">央行降准释放流动性，银行股集体上涨</h3>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">财经新闻 · 2小时前</span>
              <span class="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">重要</span>
            </div>
          </div>
          <div class="border-l-4 border-blue-500 pl-3 py-2 cursor-pointer hover:bg-gray-50 rounded-r" onclick="openNews('news3')">
            <h3 class="text-sm font-medium text-gray-900 mb-1">新能源汽车销量创新高，相关概念股活跃</h3>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">行业资讯 · 4小时前</span>
              <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">关注</span>
            </div>
          </div>
          <div class="border-l-4 border-green-500 pl-3 py-2 cursor-pointer hover:bg-gray-50 rounded-r" onclick="openNews('news4')">
            <h3 class="text-sm font-medium text-gray-900 mb-1">A股三大指数集体收涨，创业板指涨超1.5%</h3>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">市场快讯 · 6小时前</span>
              <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">利好</span>
            </div>
          </div>
        `;
      } else {
        // 显示特定股票相关新闻
        const stockNews = getStockNews(stockCode, stockName);
        newsContent.innerHTML = stockNews;
      }
    }

    function getStockNews(stockCode, stockName) {
      const newsTemplates = {
        '600519': `
          <div class="border-l-4 border-purple-500 pl-3 py-2 cursor-pointer hover:bg-gray-50 rounded-r">
            <h3 class="text-sm font-medium text-gray-900 mb-1">${stockName}三季度业绩超预期，营收同比增长15%</h3>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">公司公告 · 1小时前</span>
              <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">利好</span>
            </div>
          </div>
          <div class="border-l-4 border-blue-500 pl-3 py-2 cursor-pointer hover:bg-gray-50 rounded-r">
            <h3 class="text-sm font-medium text-gray-900 mb-1">白酒板块集体走强，${stockName}领涨带动行业情绪</h3>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">行业动态 · 3小时前</span>
              <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">关注</span>
            </div>
          </div>
        `,
        '002594': `
          <div class="border-l-4 border-green-500 pl-3 py-2 cursor-pointer hover:bg-gray-50 rounded-r">
            <h3 class="text-sm font-medium text-gray-900 mb-1">${stockName}10月新能源汽车销量突破30万辆创新高</h3>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">销量数据 · 2小时前</span>
              <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">利好</span>
            </div>
          </div>
          <div class="border-l-4 border-orange-500 pl-3 py-2 cursor-pointer hover:bg-gray-50 rounded-r">
            <h3 class="text-sm font-medium text-gray-900 mb-1">海外市场拓展顺利，${stockName}欧洲工厂正式投产</h3>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">国际业务 · 5小时前</span>
              <span class="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded">扩张</span>
            </div>
          </div>
        `,
        '300750': `
          <div class="border-l-4 border-blue-500 pl-3 py-2 cursor-pointer hover:bg-gray-50 rounded-r">
            <h3 class="text-sm font-medium text-gray-900 mb-1">${stockName}与多家车企签署长期供货协议</h3>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">合作协议 · 1小时前</span>
              <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">合作</span>
            </div>
          </div>
          <div class="border-l-4 border-purple-500 pl-3 py-2 cursor-pointer hover:bg-gray-50 rounded-r">
            <h3 class="text-sm font-medium text-gray-900 mb-1">动力电池技术突破，${stockName}发布新一代产品</h3>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">技术创新 · 4小时前</span>
              <span class="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">创新</span>
            </div>
          </div>
        `
      };

      return newsTemplates[stockCode] || `
        <div class="text-center py-4 text-gray-500">
          <i class="ri-information-line text-2xl mb-2"></i>
          <p class="text-sm">暂无${stockName}相关资讯</p>
        </div>
      `;
    }

    // 点击外部关闭下拉菜单
    document.addEventListener('click', function(event) {
      const selector = document.getElementById('stock-selector');
      const button = event.target.closest('button');

      if (!button || !button.onclick || button.onclick.toString().indexOf('toggleStockSelector') === -1) {
        selector.classList.add('hidden');
      }
    });

    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 底部导航栏交互
      const tabItems = document.querySelectorAll('.tab-item');
      tabItems.forEach(item => {
        item.addEventListener('click', function() {
          tabItems.forEach(tab => {
            const icon = tab.querySelector('i');
            const text = tab.querySelector('span');
            icon.classList.remove('text-primary');
            icon.classList.add('text-gray-500');
            text.classList.remove('text-primary');
            text.classList.add('text-gray-500');
          });

          const icon = this.querySelector('i');
          const text = this.querySelector('span');
          icon.classList.remove('text-gray-500');
          icon.classList.add('text-primary');
          text.classList.remove('text-gray-500');
          text.classList.add('text-primary');
        });
      });

      // 回车键触发分析
      document.getElementById('stockInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          analyzeStock();
        }
      });
    });

    // AI选股功能
    function showAIStockPicker() {
      window.location.href = 'AI选股.html';
    }

    // AI模拟盘功能
    function showAISimulation() {
      window.location.href = '模拟盘.html';
    }

    // 风险警示功能
    function showRiskWarning() {
      window.location.href = '风险警示.html';
    }

    // 显示实时资讯
    function showRealtimeNews() {
      if (!currentStock) return;

      const dateInfo = getCurrentDateInfo();
      const contentArea = document.getElementById('contentArea');
      contentArea.innerHTML = `
        <div class="mb-4">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">
            <i class="ri-news-line text-blue-500 mr-2"></i>${currentStock.name} 实时资讯
            <span class="text-sm text-gray-500 font-normal ml-2">${dateInfo.dateStr}</span>
          </h3>
        </div>
        <div class="space-y-4">
          ${generateRealtimeNews()}
        </div>
      `;
    }

    // 显示开盘分析
    function showOpeningAnalysis() {
      if (!currentStock) return;

      const dateInfo = getCurrentDateInfo();
      const contentArea = document.getElementById('contentArea');
      contentArea.innerHTML = `
        <div class="mb-4">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">
            <i class="ri-sun-line text-orange-500 mr-2"></i>${currentStock.name} 开盘分析
            <span class="text-sm text-gray-500 font-normal ml-2">${dateInfo.dateStr}</span>
          </h3>
        </div>
        <div class="space-y-6">
          ${generateOpeningAnalysis()}
        </div>
      `;
    }

    // 显示收盘总结
    function showClosingAnalysis() {
      if (!currentStock) return;

      const dateInfo = getCurrentDateInfo();
      if (!dateInfo.canViewClosing) {
        showClosingUnavailable();
        return;
      }

      const contentArea = document.getElementById('contentArea');
      contentArea.innerHTML = `
        <div class="mb-4">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">
            <i class="ri-moon-line text-indigo-500 mr-2"></i>${currentStock.name} 收盘总结
            <span class="text-sm text-gray-500 font-normal ml-2">${dateInfo.dateStr}</span>
          </h3>
        </div>
        <div class="space-y-6">
          ${generateClosingAnalysis()}
        </div>
      `;
    }

    // 显示收盘不可用提示
    function showClosingUnavailable() {
      const contentArea = document.getElementById('contentArea');
      const dateInfo = getCurrentDateInfo();

      contentArea.innerHTML = `
        <div class="text-center py-12">
          <div class="mb-6">
            <i class="ri-time-line text-6xl text-gray-300"></i>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">收盘总结暂不可用</h3>
          <p class="text-gray-600 mb-4">请等到今日收盘后（15:00后）再查看收盘总结</p>
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
            <div class="flex items-center justify-center mb-2">
              <i class="ri-information-line text-blue-500 mr-2"></i>
              <span class="text-sm font-medium text-blue-900">当前时间</span>
            </div>
            <div class="text-lg font-bold text-blue-700">${dateInfo.dateStr} ${dateInfo.timeStr}</div>
            <div class="text-sm text-blue-600 mt-1">
              ${dateInfo.isMarketOpen ? '交易进行中，请耐心等待收盘' : '非交易时间'}
            </div>
          </div>
          <div class="mt-6">
            <button onclick="showOpeningAnalysis()" class="bg-primary text-white px-6 py-2 rounded-lg text-sm hover:bg-blue-600 transition-colors">
              <i class="ri-sun-line mr-1"></i>查看开盘分析
            </button>
          </div>
        </div>
      `;
    }

    // 显示周报分析
    function showWeeklyReport() {
      if (!currentStock) return;

      const dateInfo = getCurrentDateInfo();
      // 计算本周的日期范围
      const today = new Date();
      const monday = new Date(today);
      monday.setDate(today.getDate() - today.getDay() + 1);
      const friday = new Date(monday);
      friday.setDate(monday.getDate() + 4);

      const weekRange = `${monday.getMonth() + 1}/${monday.getDate()} - ${friday.getMonth() + 1}/${friday.getDate()}`;

      const contentArea = document.getElementById('contentArea');
      contentArea.innerHTML = `
        <div class="mb-4">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">
            <i class="ri-calendar-line text-purple-500 mr-2"></i>${currentStock.name} AI周报分析
            <span class="text-sm text-gray-500 font-normal ml-2">本周 ${weekRange}</span>
          </h3>
        </div>
        <div class="space-y-6">
          ${generateWeeklyReport()}
        </div>
      `;
    }

    // 生成实时资讯内容
    function generateRealtimeNews() {
      const newsTemplates = {
        '600519': [
          { title: '贵州茅台三季度业绩超预期，营收同比增长15%', time: '30分钟前', source: '财经头条' },
          { title: '机构调研：茅台渠道库存健康，春节备货积极', time: '1小时前', source: '证券时报' },
          { title: '白酒板块集体走强，茅台领涨带动行业情绪', time: '2小时前', source: '新浪财经' }
        ],
        '002594': [
          { title: '比亚迪10月新能源汽车销量突破30万辆创新高', time: '45分钟前', source: '汽车之家' },
          { title: '海外市场拓展顺利，比亚迪欧洲工厂正式投产', time: '1.5小时前', source: '财联社' },
          { title: '新能源汽车补贴政策延续，利好头部企业', time: '3小时前', source: '经济观察报' }
        ]
      };

      const news = newsTemplates[currentStock.code] || [
        { title: `${currentStock.name}相关政策利好消息发布`, time: '1小时前', source: '财经网' },
        { title: `${currentStock.industry}行业景气度持续提升`, time: '2小时前', source: '证券日报' },
        { title: `机构看好${currentStock.name}长期发展前景`, time: '4小时前', source: '投资者报' }
      ];

      return news.map(item => `
        <div class="border-l-4 border-blue-500 pl-4 py-2">
          <h4 class="font-medium text-gray-900 mb-1">${item.title}</h4>
          <div class="flex items-center text-xs text-gray-500">
            <span>${item.source}</span>
            <span class="mx-2">·</span>
            <span>${item.time}</span>
          </div>
        </div>
      `).join('');
    }

    // 生成开盘分析内容
    function generateOpeningAnalysis() {
      const openPrice = (parseFloat(currentStock.price) * (0.98 + Math.random() * 0.04)).toFixed(2);
      const supportLevel = (parseFloat(currentStock.price) * 0.97).toFixed(2);
      const resistanceLevel = (parseFloat(currentStock.price) * 1.03).toFixed(2);

      return `
        <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <h4 class="font-semibold text-orange-800 mb-3">
            <i class="ri-trending-up-line mr-1"></i>盘前预测分析
          </h4>
          <div class="grid grid-cols-2 gap-4 mb-3">
            <div class="bg-white rounded p-3 text-center">
              <div class="text-lg font-bold text-orange-600">¥${openPrice}</div>
              <div class="text-xs text-gray-500">预计开盘价</div>
            </div>
            <div class="bg-white rounded p-3 text-center">
              <div class="text-lg font-bold text-blue-600">85%</div>
              <div class="text-xs text-gray-500">预测置信度</div>
            </div>
          </div>
          <p class="text-sm text-gray-700 mb-3">
            基于隔夜外盘表现和技术面分析，${currentStock.name}今日预计开盘价位在${(parseFloat(currentStock.price) * 0.98).toFixed(2)}-${(parseFloat(currentStock.price) * 1.02).toFixed(2)}区间。
          </p>
        </div>

        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 class="font-semibold text-green-800 mb-3">
            <i class="ri-lightbulb-line mr-1"></i>AI投资建议
          </h4>
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
              <span class="text-2xl font-bold text-green-600">买入</span>
              <span class="ml-2 text-xs bg-green-100 text-green-700 px-2 py-1 rounded">推荐</span>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900">目标价位</div>
              <div class="text-lg font-bold text-green-600">¥${(parseFloat(currentStock.price) * 1.08).toFixed(2)}</div>
            </div>
          </div>
          <div class="space-y-2 text-sm text-gray-700">
            <div class="flex justify-between">
              <span>支撑位：</span>
              <span class="font-medium text-red-600">¥${supportLevel}</span>
            </div>
            <div class="flex justify-between">
              <span>阻力位：</span>
              <span class="font-medium text-green-600">¥${resistanceLevel}</span>
            </div>
            <div class="flex justify-between">
              <span>建议仓位：</span>
              <span class="font-medium text-blue-600">30-50%</span>
            </div>
          </div>
        </div>

        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 class="font-semibold text-blue-800 mb-3">
            <i class="ri-information-line mr-1"></i>操作策略
          </h4>
          <div class="space-y-2 text-sm text-gray-700">
            <div class="flex items-start">
              <i class="ri-arrow-right-s-line text-blue-500 mt-0.5 mr-1"></i>
              <span>开盘后观察前30分钟走势，确认方向后再进场</span>
            </div>
            <div class="flex items-start">
              <i class="ri-arrow-right-s-line text-blue-500 mt-0.5 mr-1"></i>
              <span>如跌破支撑位¥${supportLevel}，建议减仓或止损</span>
            </div>
            <div class="flex items-start">
              <i class="ri-arrow-right-s-line text-blue-500 mt-0.5 mr-1"></i>
              <span>突破阻力位¥${resistanceLevel}，可适当加仓</span>
            </div>
            <div class="flex items-start">
              <i class="ri-arrow-right-s-line text-blue-500 mt-0.5 mr-1"></i>
              <span>建议分批建仓，控制单次买入量不超过总仓位的20%</span>
            </div>
          </div>
        </div>
      `;
    }

    // 生成收盘总结内容
    function generateClosingAnalysis() {
      const volumeChange = (Math.random() * 60 - 30).toFixed(1); // -30% 到 +30%

      return `
        <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
          <h4 class="font-semibold text-indigo-800 mb-3">
            <i class="ri-bar-chart-line mr-1"></i>今日交易总结
          </h4>
          <div class="grid grid-cols-2 gap-4 mb-3">
            <div class="bg-white rounded p-3 text-center">
              <div class="text-lg font-bold text-gray-900">¥${currentStock.price}</div>
              <div class="text-xs text-gray-500">收盘价</div>
            </div>
            <div class="bg-white rounded p-3 text-center">
              <div class="text-lg font-bold ${currentStock.change.startsWith('+') ? 'text-red-600' : 'text-green-600'}">${currentStock.change}</div>
              <div class="text-xs text-gray-500">涨跌幅</div>
            </div>
          </div>
          <p class="text-sm text-gray-700 mb-3">
            今日${currentStock.name}收盘价${currentStock.price}元，${currentStock.change}。成交量较昨日${volumeChange > 0 ? '放大' : '缩小'}${Math.abs(volumeChange)}%，
            技术面显示${Math.random() > 0.5 ? '多头' : '震荡'}格局。
          </p>
        </div>

        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 class="font-semibold text-yellow-800 mb-3">
            <i class="ri-trophy-line mr-1"></i>AI分析总结
          </h4>
          <p class="text-sm text-gray-700">
            AI模型基于技术面和基本面分析，为${currentStock.industry}板块提供专业投资建议。建议继续关注AI建议，结合个人风险偏好进行投资决策。
          </p>
        </div>

        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h4 class="font-semibold text-gray-800 mb-3">
            <i class="ri-calendar-todo-line mr-1"></i>明日展望
          </h4>
          <div class="space-y-2 text-sm text-gray-700">
            <div class="flex items-start">
              <i class="ri-arrow-right-s-line text-gray-500 mt-0.5 mr-1"></i>
              <span>预计明日开盘价位：¥${(parseFloat(currentStock.price) * (0.99 + Math.random() * 0.02)).toFixed(2)} - ¥${(parseFloat(currentStock.price) * (1.01 + Math.random() * 0.02)).toFixed(2)}</span>
            </div>
            <div class="flex items-start">
              <i class="ri-arrow-right-s-line text-gray-500 mt-0.5 mr-1"></i>
              <span>关键支撑位：¥${(parseFloat(currentStock.price) * 0.97).toFixed(2)}，关键阻力位：¥${(parseFloat(currentStock.price) * 1.03).toFixed(2)}</span>
            </div>
            <div class="flex items-start">
              <i class="ri-arrow-right-s-line text-gray-500 mt-0.5 mr-1"></i>
              <span>建议操作：${Math.random() > 0.5 ? '逢低买入，控制仓位' : '高抛低吸，注意风险'}</span>
            </div>
          </div>
        </div>
      `;
    }

    // 生成周报分析内容
    function generateWeeklyReport() {
      return `
        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <h4 class="font-semibold text-purple-800 mb-3">
            <i class="ri-bar-chart-line mr-1"></i>技术面分析
          </h4>
          <div class="grid grid-cols-2 gap-4 mb-3">
            <div>
              <div class="text-xs text-gray-500">MACD</div>
              <div class="text-sm font-medium text-green-600">金叉向上</div>
            </div>
            <div>
              <div class="text-xs text-gray-500">RSI</div>
              <div class="text-sm font-medium text-blue-600">65.8</div>
            </div>
            <div>
              <div class="text-xs text-gray-500">KDJ</div>
              <div class="text-sm font-medium text-orange-600">超买区间</div>
            </div>
            <div>
              <div class="text-xs text-gray-500">成交量</div>
              <div class="text-sm font-medium text-purple-600">温和放量</div>
            </div>
          </div>
          <p class="text-sm text-gray-700">
            技术面整体偏强，多项指标显示上涨动能充足，建议逢低布局。
          </p>
        </div>

        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 class="font-semibold text-green-800 mb-3">
            <i class="ri-building-line mr-1"></i>基本面分析
          </h4>
          <div class="space-y-2 text-sm text-gray-700">
            <div class="flex justify-between">
              <span>市盈率(PE)：</span>
              <span class="font-medium">${(Math.random() * 30 + 15).toFixed(1)}</span>
            </div>
            <div class="flex justify-between">
              <span>市净率(PB)：</span>
              <span class="font-medium">${(Math.random() * 5 + 2).toFixed(2)}</span>
            </div>
            <div class="flex justify-between">
              <span>ROE：</span>
              <span class="font-medium">${(Math.random() * 20 + 10).toFixed(1)}%</span>
            </div>
          </div>
          <p class="text-sm text-gray-700 mt-3">
            基本面稳健，${currentStock.industry}行业景气度较高，公司竞争优势明显。
          </p>
        </div>

        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 class="font-semibold text-blue-800 mb-2">
            <i class="ri-lightbulb-line mr-1"></i>AI综合评级
          </h4>
          <div class="flex items-center justify-between mb-3">
            <span class="text-2xl font-bold text-green-600">买入</span>
            <div class="flex items-center">
              <span class="text-sm text-gray-500 mr-2">目标价：</span>
              <span class="text-lg font-bold text-blue-600">¥${(parseFloat(currentStock.price) * 1.15).toFixed(2)}</span>
            </div>
          </div>
          <p class="text-sm text-gray-700">
            综合技术面和基本面分析，${currentStock.name}具备较好的投资价值，
            建议中长期持有，预期未来3-6个月有15%以上涨幅空间。
          </p>
        </div>
      `;
    }

    // 自选股管理功能
    let watchlist = JSON.parse(localStorage.getItem('stockWatchlist') || '[]');

    // 检查股票是否在自选股中
    function isInWatchlist(stockCode) {
      return watchlist.includes(stockCode);
    }

    // 添加到自选股
    function addToWatchlist(stockCode) {
      if (!isInWatchlist(stockCode)) {
        watchlist.push(stockCode);
        localStorage.setItem('stockWatchlist', JSON.stringify(watchlist));
        return true;
      }
      return false;
    }

    // 从自选股移除
    function removeFromWatchlist(stockCode) {
      const index = watchlist.indexOf(stockCode);
      if (index > -1) {
        watchlist.splice(index, 1);
        localStorage.setItem('stockWatchlist', JSON.stringify(watchlist));
        return true;
      }
      return false;
    }

    // 切换自选股状态
    function toggleWatchlist(stockCode) {
      const stockInfo = stockDatabase[stockCode];
      if (!stockInfo) return;

      if (isInWatchlist(stockCode)) {
        removeFromWatchlist(stockCode);
        showToast(`已将 ${stockInfo.name} 从自选股中移除`, 'success');
      } else {
        addToWatchlist(stockCode);
        showToast(`已将 ${stockInfo.name} 添加到自选股`, 'success');
      }

      updateWatchlistButton(stockCode);
    }

    // 更新自选股按钮状态
    function updateWatchlistButton(stockCode) {
      const btn = document.getElementById('addToWatchlistBtn');
      const icon = document.getElementById('watchlistIcon');
      const text = document.getElementById('watchlistText');

      if (!btn || !icon || !text) return;

      const inWatchlist = isInWatchlist(stockCode);

      if (inWatchlist) {
        btn.className = 'flex items-center justify-center px-3 py-3 text-sm rounded-lg transition-all duration-200 min-w-[80px] bg-yellow-100 text-yellow-700 hover:bg-yellow-200';
        icon.className = 'ri-star-fill mr-1.5';
        text.textContent = '已关注';
      } else {
        btn.className = 'flex items-center justify-center px-3 py-3 text-sm rounded-lg transition-all duration-200 min-w-[80px] bg-gray-100 text-gray-700 hover:bg-gray-200';
        icon.className = 'ri-star-line mr-1.5';
        text.textContent = '加自选';
      }
    }

    // 显示提示消息
    function showToast(message, type = 'info') {
      // 移除已存在的toast
      const existingToast = document.getElementById('toast');
      if (existingToast) {
        existingToast.remove();
      }

      // 创建新的toast
      const toast = document.createElement('div');
      toast.id = 'toast';
      toast.className = `fixed top-20 left-1/2 transform -translate-x-1/2 z-50 px-4 py-2 rounded-lg text-white text-sm font-medium transition-all duration-300 ${
        type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500'
      }`;
      toast.textContent = message;

      document.body.appendChild(toast);

      // 3秒后自动移除
      setTimeout(() => {
        if (toast) {
          toast.style.opacity = '0';
          toast.style.transform = 'translate(-50%, -20px)';
          setTimeout(() => toast.remove(), 300);
        }
      }, 3000);
    }

    // 返回功能
    function goBack() {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        // 如果没有历史记录，可以跳转到首页或显示提示
        showToast('已经是首页了', 'info');
      }
    }

    // 打开搜索页面
    function openSearch() {
      window.location.href = '股票搜索分析.html';
    }

    // 打开消息中心
    function openMessageCenter() {
      window.location.href = '消息中心.html';
    }

    // 分享功能
    function shareApp() {
      // 检查是否支持Web Share API
      if (navigator.share) {
        navigator.share({
          title: '金融智能体',
          text: '智能股票分析，AI助力投资决策',
          url: window.location.href
        }).catch(err => {
          console.log('分享失败:', err);
          fallbackShare();
        });
      } else {
        fallbackShare();
      }
    }

    // 备用分享方案
    function fallbackShare() {
      // 复制链接到剪贴板
      if (navigator.clipboard) {
        navigator.clipboard.writeText(window.location.href).then(() => {
          showToast('链接已复制到剪贴板', 'success');
        }).catch(() => {
          showShareModal();
        });
      } else {
        showShareModal();
      }
    }

    // 显示分享弹窗
    function showShareModal() {
      const modal = document.createElement('div');
      modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
      modal.innerHTML = `
        <div class="bg-white rounded-xl p-6 mx-4 w-full max-w-sm">
          <div class="text-center mb-4">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">分享金融智能体</h3>
            <p class="text-sm text-gray-600">选择分享方式</p>
          </div>
          <div class="grid grid-cols-2 gap-3 mb-4">
            <button onclick="copyLink()" class="flex flex-col items-center p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
              <i class="ri-link text-2xl text-blue-500 mb-1"></i>
              <span class="text-sm text-blue-700">复制链接</span>
            </button>
            <button onclick="shareToWechat()" class="flex flex-col items-center p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
              <i class="ri-wechat-line text-2xl text-green-500 mb-1"></i>
              <span class="text-sm text-green-700">微信分享</span>
            </button>
          </div>
          <button onclick="closeShareModal()" class="w-full py-2 border border-gray-300 rounded-lg text-sm text-gray-700 hover:bg-gray-50">
            取消
          </button>
        </div>
      `;

      document.body.appendChild(modal);

      // 点击背景关闭
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          closeShareModal();
        }
      });
    }

    // 复制链接
    function copyLink() {
      const textArea = document.createElement('textarea');
      textArea.value = window.location.href;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);

      closeShareModal();
      showToast('链接已复制到剪贴板', 'success');
    }

    // 分享到微信（实际项目中需要接入微信SDK）
    function shareToWechat() {
      closeShareModal();
      showToast('请在微信中打开此页面进行分享', 'info');
    }

    // 关闭分享弹窗
    function closeShareModal() {
      const modal = document.querySelector('.fixed.inset-0.bg-black.bg-opacity-50');
      if (modal) {
        modal.remove();
      }
    }

    // 添加到自选股功能（用于AI今日推荐）
    function addToWatchlist(stockCode, stockName) {
      // 获取当前自选股列表
      let watchlist = JSON.parse(localStorage.getItem('watchlist') || '[]');

      // 检查是否已存在
      const exists = watchlist.some(stock => stock.code === stockCode);

      if (exists) {
        showToast('该股票已在自选股中', 'info');
        return;
      }

      // 添加到自选股
      watchlist.push({
        code: stockCode,
        name: stockName,
        addTime: new Date().toISOString()
      });

      // 保存到本地存储
      localStorage.setItem('watchlist', JSON.stringify(watchlist));

      // 显示成功提示
      showToast(`${stockName} 已添加到自选股`, 'success');

      // 更新按钮状态
      event.target.textContent = '已添加';
      event.target.disabled = true;
      event.target.classList.add('text-gray-400');
    }

    // 打开AI智能体分析
    function openAIChat() {
      window.location.href = 'AI选股.html#ai-chat';
    }

    // 打开编辑模块弹窗
    function openEditModal() {
      document.getElementById('editModal').classList.remove('hidden');
    }

    // 关闭编辑模块弹窗
    function closeEditModal() {
      document.getElementById('editModal').classList.add('hidden');
    }

    // 保存模块设置
    function saveModuleSettings() {
      const checkboxes = document.querySelectorAll('#editModal input[type="checkbox"]');
      const settings = {};

      checkboxes.forEach(checkbox => {
        settings[checkbox.value] = checkbox.checked;
      });

      localStorage.setItem('moduleSettings', JSON.stringify(settings));
      showToast('设置已保存', 'success');
      closeEditModal();

      // 重新加载页面以应用设置
      setTimeout(() => {
        location.reload();
      }, 1000);
    }

    // 恢复默认设置
    function resetToDefault() {
      const checkboxes = document.querySelectorAll('#editModal input[type="checkbox"]');
      checkboxes.forEach(checkbox => {
        checkbox.checked = true;
      });
      showToast('已恢复默认设置', 'info');
    }

    // 打开新闻详情
    function openNews(newsId) {
      window.location.href = `news-detail.html?id=${newsId}`;
    }

  </script>

  <!-- 编辑模块弹窗 -->
  <div id="editModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
    <div class="bg-white rounded-xl p-6 mx-4 w-full max-w-md">
      <div class="flex items-center justify-between mb-4">
        <h3 class="font-semibold text-gray-900">编辑首页模块</h3>
        <button onclick="closeEditModal()" class="w-8 h-8 flex items-center justify-center">
          <i class="ri-close-line text-gray-500"></i>
        </button>
      </div>

      <div class="space-y-4 mb-6">
        <label class="flex items-center justify-between">
          <span class="text-sm text-gray-700">AI自选股模拟盘</span>
          <input type="checkbox" value="simulation" checked class="w-4 h-4 text-primary">
        </label>

        <label class="flex items-center justify-between">
          <span class="text-sm text-gray-700">AI分析报告</span>
          <input type="checkbox" value="reports" checked class="w-4 h-4 text-primary">
        </label>

        <label class="flex items-center justify-between">
          <span class="text-sm text-gray-700">AI今日推荐</span>
          <input type="checkbox" value="recommendations" checked class="w-4 h-4 text-primary">
        </label>

        <label class="flex items-center justify-between">
          <span class="text-sm text-gray-700">最新资讯</span>
          <input type="checkbox" value="news" checked class="w-4 h-4 text-primary">
        </label>

        <label class="flex items-center justify-between">
          <span class="text-sm text-gray-700">股票搜索分析</span>
          <input type="checkbox" value="search" checked class="w-4 h-4 text-primary">
        </label>
      </div>

      <div class="flex space-x-3">
        <button onclick="resetToDefault()" class="flex-1 py-2 border border-gray-300 rounded-lg text-sm">恢复默认</button>
        <button onclick="saveModuleSettings()" class="flex-1 py-2 bg-primary text-white rounded-lg text-sm">保存设置</button>
      </div>
    </div>
  </div>

</body>
</html>
