<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>设置 - 金融智能体</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: "#3B82F6",
            secondary: "#F1F5F9",
          },
        },
      },
    };
  </script>
</head>
<body class="bg-gray-50 min-h-screen">
  <!-- 顶部导航栏 -->
  <nav class="fixed top-0 w-full bg-white z-50 px-4 py-3 border-b border-gray-100">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <button onclick="goBack()" class="w-8 h-8 flex items-center justify-center mr-3 cursor-pointer">
          <i class="ri-arrow-left-line text-gray-600 text-lg"></i>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">设置</h1>
      </div>
      <div class="flex items-center space-x-2">
        <button onclick="shareApp()" class="w-8 h-8 flex items-center justify-center">
          <div class="flex space-x-0.5">
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
            <div class="w-1 h-1 bg-gray-600 rounded-full"></div>
          </div>
        </button>
        <button class="w-8 h-8 flex items-center justify-center">
          <div class="w-5 h-5 border-2 border-gray-600 rounded-full flex items-center justify-center">
            <div class="w-2 h-2 bg-gray-600 rounded-full"></div>
          </div>
        </button>
      </div>
    </div>
  </nav>

  <!-- 主要内容区域 -->
  <main class="pt-16 pb-20 px-4">
    <!-- 通知设置 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <h3 class="font-semibold text-gray-900 mb-4">通知设置</h3>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <i class="ri-notification-line text-primary mr-3"></i>
              <div>
                <div class="font-medium text-gray-900">推送通知</div>
                <div class="text-sm text-gray-500">接收重要消息和提醒</div>
              </div>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" class="sr-only peer" checked>
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
          
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <i class="ri-stock-line text-primary mr-3"></i>
              <div>
                <div class="font-medium text-gray-900">股价提醒</div>
                <div class="text-sm text-gray-500">股价异动和目标价提醒</div>
              </div>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" class="sr-only peer" checked>
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
          
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <i class="ri-file-text-line text-primary mr-3"></i>
              <div>
                <div class="font-medium text-gray-900">报告推送</div>
                <div class="text-sm text-gray-500">AI分析报告生成通知</div>
              </div>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      </div>
    </section>

    <!-- 数据设置 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <h3 class="font-semibold text-gray-900 mb-4">数据设置</h3>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <i class="ri-wifi-line text-primary mr-3"></i>
              <div>
                <div class="font-medium text-gray-900">仅WiFi更新</div>
                <div class="text-sm text-gray-500">仅在WiFi环境下更新数据</div>
              </div>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
          
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <i class="ri-download-cloud-line text-primary mr-3"></i>
              <div>
                <div class="font-medium text-gray-900">数据缓存</div>
                <div class="text-sm text-gray-500">本地缓存历史数据</div>
              </div>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" class="sr-only peer" checked>
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
          
          <button onclick="clearCache()" class="flex items-center w-full p-3 text-left hover:bg-gray-50 rounded-lg">
            <i class="ri-delete-bin-line text-red-500 mr-3"></i>
            <div>
              <div class="font-medium text-gray-900">清除缓存</div>
              <div class="text-sm text-gray-500">清除本地缓存数据</div>
            </div>
          </button>
        </div>
      </div>
    </section>

    <!-- 账户设置 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <h3 class="font-semibold text-gray-900 mb-4">账户设置</h3>
        <div class="space-y-4">
          <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
            <div class="flex items-center">
              <i class="ri-wechat-line text-green-500 mr-3"></i>
              <div>
                <div class="font-medium text-gray-900">微信登录</div>
                <div class="text-sm text-gray-500">已绑定微信账号</div>
              </div>
            </div>
            <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">已绑定</span>
          </div>

          <button onclick="logout()" class="flex items-center w-full p-3 text-left hover:bg-gray-50 rounded-lg">
            <i class="ri-logout-circle-line text-red-500 mr-3"></i>
            <div>
              <div class="font-medium text-gray-900">退出登录</div>
              <div class="text-sm text-gray-500">退出当前微信账号</div>
            </div>
            <i class="ri-arrow-right-s-line text-gray-400 ml-auto"></i>
          </button>
        </div>
      </div>
    </section>

    <!-- 其他设置 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <h3 class="font-semibold text-gray-900 mb-4">其他设置</h3>
        <div class="space-y-4">
          <button onclick="clearCache()" class="flex items-center w-full p-3 text-left hover:bg-gray-50 rounded-lg">
            <i class="ri-delete-bin-line text-primary mr-3"></i>
            <div>
              <div class="font-medium text-gray-900">清除缓存</div>
              <div class="text-sm text-gray-500">清除本地缓存数据</div>
            </div>
            <i class="ri-arrow-right-s-line text-gray-400 ml-auto"></i>
          </button>

          <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div class="flex items-center">
              <i class="ri-information-line text-gray-500 mr-3"></i>
              <div>
                <div class="font-medium text-gray-900">当前版本</div>
                <div class="text-sm text-gray-500">v2.1.0</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <script>
    function goBack() {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        window.location.href = '我的.html';
      }
    }

    function shareApp() {
      if (navigator.share) {
        navigator.share({
          title: '金融智能体 - 设置',
          text: '个性化设置您的投资助手',
          url: window.location.href
        }).catch(err => {
          console.log('分享失败:', err);
        });
      } else {
        if (navigator.clipboard) {
          navigator.clipboard.writeText(window.location.href).then(() => {
            alert('链接已复制到剪贴板');
          });
        }
      }
    }

    function clearCache() {
      if (confirm('确定要清除所有缓存数据吗？这将删除本地保存的历史数据。')) {
        // 模拟清除缓存
        setTimeout(() => {
          alert('缓存已清除完成！');
        }, 1000);
      }
    }

    function logout() {
      if (confirm('确定要退出登录吗？')) {
        alert('已退出登录，感谢使用金融智能体！');
        // 这里可以添加实际的退出登录逻辑
        // 比如清除本地存储的用户信息等
      }
    }

    // 主题色彩切换
    document.addEventListener('DOMContentLoaded', function() {
      const colorButtons = document.querySelectorAll('.w-6.h-6.rounded-full');
      colorButtons.forEach(button => {
        button.addEventListener('click', function() {
          // 移除所有按钮的边框
          colorButtons.forEach(btn => {
            btn.classList.remove('border-blue-600', 'border-green-600', 'border-purple-600', 'border-red-600');
            btn.classList.add('border-transparent');
          });
          
          // 为当前按钮添加边框
          const bgColor = this.classList.find(cls => cls.includes('bg-'));
          if (bgColor.includes('blue')) {
            this.classList.add('border-blue-600');
          } else if (bgColor.includes('green')) {
            this.classList.add('border-green-600');
          } else if (bgColor.includes('purple')) {
            this.classList.add('border-purple-600');
          } else if (bgColor.includes('red')) {
            this.classList.add('border-red-600');
          }
          this.classList.remove('border-transparent');
        });
      });
    });
  </script>
</body>
</html>
