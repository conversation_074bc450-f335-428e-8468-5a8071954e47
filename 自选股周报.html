<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>自选股周报 - 金融智能体</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
  <style>
    .gradient-bg {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .report-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .stock-card {
      transition: all 0.3s ease;
    }
    .stock-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    .trend-up { color: #ef4444; }
    .trend-down { color: #22c55e; }
    .weekly-summary {
      background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    }
    .performance-chart {
      background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    }
  </style>
</head>
<body class="bg-gray-50">
  <!-- 顶部导航栏 -->
  <header class="bg-white shadow-sm fixed top-0 left-0 right-0 z-50">
    <div class="flex items-center justify-between px-4 py-3">
      <div class="flex items-center space-x-3">
        <button onclick="goBack()" class="p-2 hover:bg-gray-100 rounded-lg">
          <i class="ri-arrow-left-line text-xl text-gray-600"></i>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">自选股周报</h1>
      </div>
      <div class="flex items-center space-x-2">
        <button onclick="shareReport()" class="p-2 hover:bg-gray-100 rounded-lg">
          <i class="ri-share-line text-xl text-gray-600"></i>
        </button>
        <button onclick="refreshReport()" class="p-2 hover:bg-gray-100 rounded-lg">
          <i class="ri-refresh-line text-xl text-gray-600"></i>
        </button>
      </div>
    </div>
  </header>

  <!-- 主要内容区域 -->
  <main class="pt-16 pb-20 px-4">
    <!-- 报告头部 -->
    <section class="mb-6">
      <div class="report-header rounded-xl p-6 text-white">
        <div class="flex items-center justify-between mb-4">
          <div>
            <h2 class="text-2xl font-bold mb-1">自选股周报</h2>
            <p class="text-sm opacity-90" id="week-range">2024年1月8日 - 1月12日</p>
          </div>
          <div class="text-right">
            <div class="text-sm opacity-90">本周表现</div>
            <div class="text-lg font-semibold text-green-300" id="week-performance">+2.8%</div>
          </div>
        </div>
        <div class="grid grid-cols-2 gap-4">
          <div class="bg-white bg-opacity-20 rounded-lg p-3">
            <div class="text-sm opacity-90 mb-1">上涨股票</div>
            <div class="text-lg font-semibold" id="up-stocks">3只</div>
          </div>
          <div class="bg-white bg-opacity-20 rounded-lg p-3">
            <div class="text-sm opacity-90 mb-1">下跌股票</div>
            <div class="text-lg font-semibold" id="down-stocks">1只</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 本周市场回顾 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <h3 class="font-semibold text-gray-900 mb-4 flex items-center">
          <i class="ri-calendar-week-line text-blue-500 mr-2"></i>
          本周市场回顾
        </h3>
        <div class="weekly-summary rounded-lg p-4 mb-4">
          <div class="grid grid-cols-3 gap-4 text-center mb-4">
            <div>
              <div class="text-lg font-bold text-gray-900">+1.2%</div>
              <div class="text-xs text-gray-600">上证指数</div>
            </div>
            <div>
              <div class="text-lg font-bold text-gray-900">+2.1%</div>
              <div class="text-xs text-gray-600">深证成指</div>
            </div>
            <div>
              <div class="text-lg font-bold text-gray-900">+3.5%</div>
              <div class="text-xs text-gray-600">创业板指</div>
            </div>
          </div>
        </div>
        <div class="text-sm text-gray-700 leading-relaxed space-y-2">
          <p>
            <span class="font-medium">市场表现：</span>
            本周A股市场整体表现良好，三大指数均收涨。创业板指表现最为强劲，周涨幅达3.5%。
            市场成交量较上周放大20%，投资者情绪明显回暖。
          </p>
          <p>
            <span class="font-medium">热点轮动：</span>
            新能源汽车、人工智能板块领涨，传统制造业也有不错表现。
            金融、地产板块相对疲软，但估值修复预期仍存。
          </p>
          <p>
            <span class="font-medium">资金流向：</span>
            北向资金本周净流入85亿元，外资持续看好A股长期价值。
            主力资金重点关注科技成长股和低估值蓝筹股。
          </p>
        </div>
      </div>
    </section>

    <!-- 自选股周度表现 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <h3 class="font-semibold text-gray-900 mb-4 flex items-center">
          <i class="ri-bar-chart-line text-green-500 mr-2"></i>
          自选股周度表现
        </h3>
        <div class="space-y-4" id="weekly-performance">
          <!-- 股票周度表现将通过JavaScript动态生成 -->
        </div>
      </div>
    </section>

    <!-- 技术面分析 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <h3 class="font-semibold text-gray-900 mb-4 flex items-center">
          <i class="ri-line-chart-line text-purple-500 mr-2"></i>
          技术面分析
        </h3>
        <div class="space-y-4">
          <div class="performance-chart rounded-lg p-4">
            <h4 class="font-medium text-gray-900 mb-3">技术指标概览</h4>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span class="text-gray-600">MACD金叉股票</span>
                  <span class="font-medium text-green-600">2只</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">RSI超买区间</span>
                  <span class="font-medium text-red-600">1只</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">突破重要阻力</span>
                  <span class="font-medium text-blue-600">1只</span>
                </div>
              </div>
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span class="text-gray-600">成交量放大</span>
                  <span class="font-medium text-green-600">3只</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">均线多头排列</span>
                  <span class="font-medium text-blue-600">2只</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">支撑位有效</span>
                  <span class="font-medium text-green-600">4只</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="text-sm text-gray-700 leading-relaxed">
            <p class="mb-2">
              <span class="font-medium">技术面总结：</span>
              本周自选股技术面整体向好，多数股票呈现多头格局。
              比亚迪和贵州茅台技术指标最为强劲，建议继续持有。
            </p>
            <p>
              <span class="font-medium">关键位置：</span>
              宁德时代需关注200元整数关口突破情况，中国平安在45元附近有较强支撑。
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- 下周投资策略 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <h3 class="font-semibold text-gray-900 mb-4 flex items-center">
          <i class="ri-lightbulb-line text-yellow-500 mr-2"></i>
          下周投资策略
        </h3>
        <div class="space-y-4">
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 class="font-medium text-blue-800 mb-2">
              <i class="ri-trending-up-line mr-1"></i>积极配置
            </h4>
            <div class="text-sm text-blue-700 space-y-1">
              <p>• <strong>比亚迪</strong>：技术面突破，建议加仓至目标仓位</p>
              <p>• <strong>贵州茅台</strong>：基本面稳健，可逢低增持</p>
            </div>
          </div>
          
          <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 class="font-medium text-yellow-800 mb-2">
              <i class="ri-eye-line mr-1"></i>观察等待
            </h4>
            <div class="text-sm text-yellow-700 space-y-1">
              <p>• <strong>宁德时代</strong>：等待突破确认，暂时观望</p>
            </div>
          </div>
          
          <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <h4 class="font-medium text-red-800 mb-2">
              <i class="ri-trending-down-line mr-1"></i>谨慎操作
            </h4>
            <div class="text-sm text-red-700 space-y-1">
              <p>• <strong>中国平安</strong>：基本面承压，建议减仓或止损</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 重要事件提醒 -->
    <section class="mb-6">
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <h3 class="font-semibold text-gray-900 mb-4 flex items-center">
          <i class="ri-calendar-event-line text-red-500 mr-2"></i>
          下周重要事件
        </h3>
        <div class="space-y-3">
          <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
            <div class="text-xs bg-red-100 text-red-700 px-2 py-1 rounded font-medium">周一</div>
            <div class="flex-1">
              <div class="font-medium text-gray-900">贵州茅台年度业绩预告</div>
              <div class="text-sm text-gray-600">预计业绩增长15-20%，关注市场反应</div>
            </div>
          </div>
          <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
            <div class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded font-medium">周三</div>
            <div class="flex-1">
              <div class="font-medium text-gray-900">比亚迪月度销量数据</div>
              <div class="text-sm text-gray-600">关注新能源汽车销量是否持续增长</div>
            </div>
          </div>
          <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
            <div class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded font-medium">周五</div>
            <div class="flex-1">
              <div class="font-medium text-gray-900">央行货币政策会议</div>
              <div class="text-sm text-gray-600">关注流动性政策变化对市场影响</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 报告说明 -->
    <section class="mb-6">
      <div class="bg-gray-50 rounded-xl p-4">
        <div class="flex items-center justify-between text-xs text-gray-500 mb-2">
          <div class="flex items-center">
            <i class="ri-robot-line mr-1"></i>
            <span>AI智投分析引擎生成</span>
          </div>
          <div id="generate-time">生成时间：2024-01-15 18:00</div>
        </div>
        <div class="text-xs text-gray-400 leading-relaxed">
          本周报基于您的自选股一周表现和市场数据，通过AI算法深度分析生成。
          报告内容仅供参考，不构成投资建议。投资有风险，入市需谨慎。
        </div>
      </div>
    </section>
  </main>

  <!-- 底部导航栏 -->
  <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
    <div class="grid grid-cols-4 py-2">
      <a href="index.html" class="flex flex-col items-center py-2 text-gray-600">
        <i class="ri-home-line text-xl mb-1"></i>
        <span class="text-xs">首页</span>
      </a>
      <a href="watchlist.html" class="flex flex-col items-center py-2 text-gray-600">
        <i class="ri-star-line text-xl mb-1"></i>
        <span class="text-xs">自选股</span>
      </a>
      <a href="reports.html" class="flex flex-col items-center py-2 text-primary">
        <i class="ri-file-text-line text-xl mb-1"></i>
        <span class="text-xs">报告中心</span>
      </a>
      <a href="关于我们.html" class="flex flex-col items-center py-2 text-gray-600">
        <i class="ri-user-line text-xl mb-1"></i>
        <span class="text-xs">我的</span>
      </a>
    </div>
  </nav>

  <script>
    // 自选股周度数据
    const weeklyData = [
      {
        name: '贵州茅台',
        code: '600519',
        weekChange: '+5.2%',
        weekAmount: '+82.50',
        currentPrice: '1678.50',
        weekHigh: '1695.00',
        weekLow: '1596.00',
        analysis: '本周表现强劲，突破前期高点，白酒板块估值修复行情延续，基本面支撑有力。',
        trend: 'up',
        technical: 'MACD金叉，RSI进入强势区间，成交量有效放大',
        suggestion: '持有'
      },
      {
        name: '比亚迪',
        code: '002594',
        weekChange: '+8.7%',
        weekAmount: '+19.65',
        currentPrice: '245.80',
        weekHigh: '248.50',
        weekLow: '226.15',
        analysis: '新能源汽车销量数据亮眼，技术面突破关键阻力位，多头格局确立。',
        trend: 'up',
        technical: '突破240元阻力位，均线多头排列，成交量持续放大',
        suggestion: '买入'
      },
      {
        name: '宁德时代',
        code: '300750',
        weekChange: '-2.1%',
        weekAmount: '-4.00',
        currentPrice: '185.20',
        weekHigh: '195.50',
        weekLow: '182.30',
        analysis: '受原材料成本压力影响，股价有所回调，但在重要支撑位获得支撑。',
        trend: 'down',
        technical: '在180-200区间震荡，等待方向选择，支撑位有效',
        suggestion: '观望'
      },
      {
        name: '中国平安',
        code: '601318',
        weekChange: '-3.8%',
        weekAmount: '-1.68',
        currentPrice: '42.50',
        weekHigh: '44.85',
        weekLow: '41.20',
        analysis: '保险业务承压，投资收益下滑，估值虽低但基本面仍需观察。',
        trend: 'down',
        technical: 'RSI进入超卖区间，但下跌趋势未改变，需关注支撑',
        suggestion: '减仓'
      }
    ];

    // 生成周度表现内容
    function generateWeeklyPerformance() {
      const container = document.getElementById('weekly-performance');
      let html = '';

      weeklyData.forEach(stock => {
        const trendClass = stock.trend === 'up' ? 'trend-up' : 'trend-down';
        const suggestionColor = getSuggestionColor(stock.suggestion);
        
        html += `
          <div class="stock-card border border-gray-200 rounded-lg p-4">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center space-x-3">
                <div>
                  <h4 class="font-semibold text-gray-900">${stock.name}</h4>
                  <div class="text-xs text-gray-500">${stock.code}</div>
                </div>
              </div>
              <div class="text-right">
                <div class="text-lg font-bold text-gray-900">¥${stock.currentPrice}</div>
                <div class="text-sm ${trendClass}">${stock.weekChange}</div>
              </div>
            </div>
            
            <div class="grid grid-cols-3 gap-3 mb-3 text-xs">
              <div>
                <span class="text-gray-500">周涨跌额：</span>
                <div class="${trendClass} font-medium">${stock.weekAmount}</div>
              </div>
              <div>
                <span class="text-gray-500">周最高：</span>
                <div class="text-gray-900">¥${stock.weekHigh}</div>
              </div>
              <div>
                <span class="text-gray-500">周最低：</span>
                <div class="text-gray-900">¥${stock.weekLow}</div>
              </div>
            </div>
            
            <div class="mb-3">
              <div class="text-sm text-gray-700 leading-relaxed mb-2">${stock.analysis}</div>
              <div class="text-xs text-gray-600 bg-gray-50 rounded p-2">
                <span class="font-medium">技术面：</span>${stock.technical}
              </div>
            </div>
            
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <span class="text-xs ${suggestionColor} px-2 py-1 rounded">${stock.suggestion}</span>
              </div>
              <button onclick="viewStockDetail('${stock.code}', '${stock.name}')" 
                      class="text-xs text-primary hover:text-blue-700">
                查看详情 <i class="ri-arrow-right-s-line"></i>
              </button>
            </div>
          </div>
        `;
      });

      container.innerHTML = html;
    }

    // 获取建议颜色
    function getSuggestionColor(suggestion) {
      switch(suggestion) {
        case '买入': return 'bg-green-100 text-green-700';
        case '持有': return 'bg-blue-100 text-blue-700';
        case '观望': return 'bg-yellow-100 text-yellow-700';
        case '减仓': return 'bg-red-100 text-red-700';
        default: return 'bg-gray-100 text-gray-700';
      }
    }

    // 查看股票详情
    function viewStockDetail(code, name) {
      window.location.href = `股票分析.html?code=${code}&name=${encodeURIComponent(name)}`;
    }

    // 返回功能
    function goBack() {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        window.location.href = 'reports.html';
      }
    }

    // 分享报告
    function shareReport() {
      if (navigator.share) {
        navigator.share({
          title: '自选股周报 - 金融智能体',
          text: '查看我的自选股本周表现和投资策略',
          url: window.location.href
        }).catch(err => {
          console.log('分享失败:', err);
        });
      } else {
        if (navigator.clipboard) {
          navigator.clipboard.writeText(window.location.href).then(() => {
            alert('链接已复制到剪贴板');
          });
        }
      }
    }

    // 刷新报告
    function refreshReport() {
      const refreshBtn = document.querySelector('[onclick="refreshReport()"] i');
      refreshBtn.classList.add('animate-spin');
      
      setTimeout(() => {
        refreshBtn.classList.remove('animate-spin');
        generateWeeklyPerformance();
        alert('周报已更新');
      }, 1500);
    }

    // 初始化页面
    document.addEventListener('DOMContentLoaded', function() {
      // 设置周报时间范围
      const now = new Date();
      const weekStart = new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000);
      const weekEnd = new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000);
      
      const weekRange = `${weekStart.toLocaleDateString('zh-CN', { month: 'long', day: 'numeric' })} - ${weekEnd.toLocaleDateString('zh-CN', { month: 'long', day: 'numeric' })}`;
      document.getElementById('week-range').textContent = weekRange;
      document.getElementById('generate-time').textContent = `生成时间：${now.toLocaleString('zh-CN')}`;
      
      // 计算统计数据
      const upStocks = weeklyData.filter(stock => stock.trend === 'up').length;
      const downStocks = weeklyData.filter(stock => stock.trend === 'down').length;
      
      document.getElementById('up-stocks').textContent = `${upStocks}只`;
      document.getElementById('down-stocks').textContent = `${downStocks}只`;
      
      // 生成周度表现
      generateWeeklyPerformance();
    });
  </script>
</body>
</html>
